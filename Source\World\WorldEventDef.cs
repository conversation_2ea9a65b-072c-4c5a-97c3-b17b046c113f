using Verse;
using System.Collections.Generic;
using NationalSystemMod.Economy;
using RimWorld.Planet; // For TileFinder

namespace NationalSystemMod.World
{
    public class WorldEventDef : Def
    {
        public string description;
        public float commonality = 1.0f; // How often this event appears (relative to others).
        public int minDurationDays = 5;
        public int maxDurationDays = 20;

        public bool affectsSingleTile = false;
        public bool affectsFaction = false; // If it targets a specific faction.
        public bool affectsAllFactions = false; // If it's a global event.

        // Resource effects (per day, per affected entity).
        public Dictionary<NationalResourceType, float> resourceGainPerDay;
        public Dictionary<NationalResourceType, float> resourceLossPerDay;

        // Influence effects.
        public float influenceGainPerDay = 0f;
        public float influenceLossPerDay = 0f;

        // Military effects.
        public float militaryPowerGain = 0f; // One-time gain.
        public float militaryPowerLoss = 0f; // One-time loss.
        public float militaryUpkeepModifier = 1.0f; // Multiplier for upkeep.

        // Diplomatic effects (how this event influences relations between factions).
        // e.g., A shared plague might increase goodwill, a border dispute decreases.
        public float goodwillChangeFactor = 0f; // Added/subtracted directly.

        // Custom effects (e.g., spawns a new army, changes a tile's resource output permanently).
        public List<string> customEffectTags;

        // For affectedSingleTile events, filter for valid tiles (e.g., only fertile, only owned by a faction).
        public List<string> tileTagsFilter; // e.g., "Fertile", "OwnedByFaction"
        
        // For affectsFaction events, filter for valid faction types (e.g., "Outlander", "Tribal").
        public List<string> factionDefNamesFilter; // e.g., "TribeRough", "OutlanderCivil"

        // For affectsFaction events, define a required goodwill range for the target.
        public FloatRange targetGoodwillRange = new FloatRange(-100f, 100f);

        // Properties for Scribe to handle collections.
        private Dictionary<NationalResourceType, float> _resourceGainPerDay;
        public Dictionary<NationalResourceType, float> ResourceGainPerDay
        {
            get => _resourceGainPerDay;
            set => _resourceGainPerDay = value;
        }

        private Dictionary<NationalResourceType, float> _resourceLossPerDay;
        public Dictionary<NationalResourceType, float> ResourceLossPerDay
        {
            get => _resourceLossPerDay;
            set => _resourceLossPerDay = value;
        }

        private List<string> _customEffectTags;
        public List<string> CustomEffectTags
        {
            get => _customEffectTags;
            set => _customEffectTags = value;
        }

        private List<string> _tileTagsFilter;
        public List<string> TileTagsFilter
        {
            get => _tileTagsFilter;
            set => _tileTagsFilter = value;
        }

        private List<string> _factionDefNamesFilter;
        public List<string> FactionDefNamesFilter
        {
            get => _factionDefNamesFilter;
            set => _factionDefNamesFilter = value;
        }


        public override void PostLoad()
        {
            base.PostLoad();
            if (_resourceGainPerDay == null && resourceGainPerDay != null) _resourceGainPerDay = resourceGainPerDay;
            if (_resourceLossPerDay == null && resourceLossPerDay != null) _resourceLossPerDay = resourceLossPerDay;
            if (_customEffectTags == null && customEffectTags != null) _customEffectTags = customEffectTags;
            if (_tileTagsFilter == null && tileTagsFilter != null) _tileTagsFilter = tileTagsFilter;
            if (_factionDefNamesFilter == null && factionDefNamesFilter != null) _factionDefNamesFilter = factionDefNamesFilter;

            resourceGainPerDay = null;
            resourceLossPerDay = null;
customEffectTags = null;
            tileTagsFilter = null;
            factionDefNamesFilter = null;
        }

        public override IEnumerable<string> ConfigErrors()
        {
            foreach (string error in base.ConfigErrors()) yield return error;
            if (!affectsSingleTile && !affectsFaction && !affectsAllFactions)
            {
                yield return "WorldEventDef must affect at least one target type (singleTile, faction, or allFactions).";
            }
        }
    }
} 