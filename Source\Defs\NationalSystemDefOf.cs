using RimWorld;
using Verse;
using AIGen.NationalSystemMod.Research;

namespace AIGen.NationalSystemMod.Defs
{
    [DefOf]
    public static class NationalSystemDefOf
    {
        // Add existing Defs here if they need to be referenced from code.
        // public static NationalPolicyDef SomePolicy;

        // NEW: For WorldArmy
        public static WorldObjectDef NationalSystem_WorldArmy;

        // National Policies
        public static NationalPolicyDef NationalPolicy_Expansionist;
        public static NationalPolicyDef NationalPolicy_Militarist;
        public static NationalPolicyDef NationalPolicy_Diplomatic;
        public static NationalPolicyDef NationalPolicy_Isolationist;

        // NEW: For NationalResearchDef
        public static NationalResearchDef NationalResearch_BasicLogistics;
        public static NationalResearchDef NationalResearch_ImprovedProduction;
        public static NationalResearchDef NationalResearch_MilitaryDoctrine;
        public static NationalResearchDef NationalResearch_DiplomaticInitiative;
        public static NationalResearchDef NationalResearch_StrategicIsolationism;

        static NationalSystemDefOf()
        {
            DefOfHelper.EnsureInitializedInCtor(typeof(NationalSystemDefOf));
        }
    }
} 