using RimWorld;
using Verse;

namespace AIGen.NationalSystemMod.Defs
{
    [DefOf]
    public static class NationalSystemDefOf
    {
        // Add existing Defs here if they need to be referenced from code.
        // public static NationalPolicyDef SomePolicy;

        // NEW: For WorldArmy
        public static WorldObjectDef NationalSystem_WorldArmy;

        // National Policies
        public static AIGen.NationalSystemMod.Defs.NationalPolicyDef NS_ExpansionistPolicy;
        public static AIGen.NationalSystemMod.Defs.NationalPolicyDef NS_MilitaristicPolicy;
        public static AIGen.NationalSystemMod.Defs.NationalPolicyDef NS_TraderPolicy;
        public static AIGen.NationalSystemMod.Defs.NationalPolicyDef NS_IsolationistPolicy;

        // NEW: For NationalPolicyDef
        public static AIGen.NationalSystemMod.Defs.NationalPolicyDef NationalPolicy_Expansionist;
        public static AIGen.NationalSystemMod.Defs.NationalPolicyDef NationalPolicy_Trader;
        public static AIGen.NationalSystemMod.Defs.NationalPolicyDef NationalPolicy_Technocratic;
        public static AIGen.NationalSystemMod.Defs.NationalPolicyDef NationalPolicy_Isolationist;

        // NEW: For NationalResearchDef
        public static AIGen.NationalSystemMod.Research.NationalResearchDef NationalResearch_BasicLogistics;
        public static AIGen.NationalSystemMod.Research.NationalResearchDef NationalResearch_ImprovedProduction;
        public static AIGen.NationalSystemMod.Research.NationalResearchDef NationalResearch_MilitaryDoctrine;
        public static AIGen.NationalSystemMod.Research.NationalResearchDef NationalResearch_DiplomaticInitiative;
        public static AIGen.NationalSystemMod.Research.NationalResearchDef NationalResearch_StrategicIsolationism;

        // NEW: For WorldEventDef
        public static AIGen.NationalSystemMod.World.WorldEventDef WorldEvent_ResourceBoom;
        public static AIGen.NationalSystemMod.World.WorldEventDef WorldEvent_Famine;
        public static AIGen.NationalSystemMod.World.WorldEventDef WorldEvent_LocalRebellion;
        public static AIGen.NationalSystemMod.World.WorldEventDef WorldEvent_TechnologicalBreakthrough;

        static NationalSystemDefOf()
        {
            DefOfHelper.EnsureInitializedInCtor(typeof(NationalSystemDefOf));
        }
    }
} 