using RimWorld;
using Verse;
using NationalSystemMod.Research;
using NationalSystemMod.World;

namespace NationalSystemMod.Defs
{
    [DefOf]
    public static class NationalSystemDefOf
    {
        // Add existing Defs here if they need to be referenced from code.
        // public static NationalPolicyDef SomePolicy;

        // NEW: For WorldArmy
        public static WorldObjectDef NationalSystem_WorldArmy;

        // National Policies
        public static NationalPolicyDef NS_ExpansionistPolicy;
        public static NationalPolicyDef NS_MilitaristicPolicy;
        public static NationalPolicyDef NS_TraderPolicy;
        public static NationalPolicyDef NS_IsolationistPolicy;

        // NEW: For NationalPolicyDef
        public static NationalPolicyDef NationalPolicy_Expansionist;
        public static NationalPolicyDef NationalPolicy_Trader;
        public static NationalPolicyDef NationalPolicy_Technocratic;
        public static NationalPolicyDef NationalPolicy_Isolationist;

        // NEW: For NationalResearchDef
        public static NationalResearchDef NationalResearch_BasicLogistics;
        public static NationalResearchDef NationalResearch_ImprovedProduction;
        public static NationalResearchDef NationalResearch_MilitaryDoctrine;
        public static NationalResearchDef NationalResearch_DiplomaticInitiative;
        public static NationalResearchDef NationalResearch_StrategicIsolationism;

        // NEW: For WorldEventDef
        public static WorldEventDef WorldEvent_ResourceBoom;
        public static WorldEventDef WorldEvent_Famine;
        public static WorldEventDef WorldEvent_LocalRebellion;
        public static WorldEventDef WorldEvent_TechnologicalBreakthrough;

        static NationalSystemDefOf()
        {
            DefOfHelper.EnsureInitializedInCtor(typeof(NationalSystemDefOf));
        }
    }
} 