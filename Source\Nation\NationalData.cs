using Verse;
using System.Collections.Generic;
using AIGen.NationalSystemMod.Defs;
using AIGen.NationalSystemMod.Economy;
using AIGen.NationalSystemMod.StrategicAI;
using AIGen.NationalSystemMod.Research; // NEW: For NationalResearchManager.
using UnityEngine;
using RimWorld;

namespace AIGen.NationalSystemMod.Nation
{
    public class NationalData : IExposable
    {
        public Faction Faction; 

        public float TotalInfluenceScore;
        public int ControlledTilesCount;

        public float NationalWealth;
        public float MilitaryPower;

        public NationalPolicyDef CurrentPolicy;

        public NationalResources Resources;

        public StrategicGoalType CurrentStrategicGoal;

        // NEW: National Research Manager.
        public NationalResearchManager Research;

        // Property for easier access
        public NationalResearchManager ResearchManager => Research;

        // NEW: List of policies unlocked/available to this faction.
        // Factions start with some basic policies, and unlock more via research.
        public List<NationalPolicyDef> UnlockedPolicies;

        public NationalData() { }

        public NationalData(Faction faction)
        {
            this.Faction = faction;
            this.TotalInfluenceScore = 0f;
            this.ControlledTilesCount = 0;
            this.NationalWealth = 100f;
            this.MilitaryPower = 50f;
            this.CurrentPolicy = null;
            this.Resources = new NationalResources();
            this.CurrentStrategicGoal = StrategicGoalType.EconomicGrowth;
            this.Research = new NationalResearchManager(faction); // Initialize research manager.
            
            // Initialize UnlockedPolicies with starting policies.
            UnlockedPolicies = new List<NationalPolicyDef>();
            // Add some default policies available from start.
            if (DefDatabase<NationalPolicyDef>.GetNamedSilentFail("NationalPolicy_Expansionist") != null) UnlockedPolicies.Add(DefDatabase<NationalPolicyDef>.GetNamed("NationalPolicy_Expansionist"));
            if (DefDatabase<NationalPolicyDef>.GetNamedSilentFail("NationalPolicy_Trader") != null) UnlockedPolicies.Add(DefDatabase<NationalPolicyDef>.GetNamed("NationalPolicy_Trader"));
            // Set initial policy (e.g., Expansionist if it exists).
            this.CurrentPolicy = UnlockedPolicies.FirstOrDefault();
        }

        public bool IsValid()
        {
            return Faction != null && !Faction.Hidden && Faction.def.humanlikeFaction;
        }

        public void ExposeData()
        {
            Scribe_References.Look(ref Faction, "faction");
            Scribe_Values.Look(ref TotalInfluenceScore, "totalInfluenceScore", 0f);
            Scribe_Values.Look(ref ControlledTilesCount, "controlledTilesCount", 0);
            Scribe_Values.Look(ref NationalWealth, "nationalWealth", 100f);
            Scribe_Values.Look(ref MilitaryPower, "militaryPower", 50f);
            Scribe_Defs.Look(ref CurrentPolicy, "currentPolicy");
            
            Scribe_Deep.Look(ref Resources, "resources");
            if (Scribe.mode == LoadSaveMode.PostLoadInit && Resources == null)
            {
                Resources = new NationalResources();
            }

            Scribe_Values.Look(ref CurrentStrategicGoal, "currentStrategicGoal", StrategicGoalType.EconomicGrowth);

            // NEW: Scribe NationalResearchManager.
            Scribe_Deep.Look(ref Research, "research");
            if (Scribe.mode == LoadSaveMode.PostLoadInit && Research == null)
            {
                Research = new NationalResearchManager(Faction); // Ensure it's initialized on load if missing.
            }

            // NEW: Scribe UnlockedPolicies.
            Scribe_Collections.Look(ref UnlockedPolicies, "unlockedPolicies", LookMode.Def);
            if (Scribe.mode == LoadSaveMode.PostLoadInit && UnlockedPolicies == null)
            {
                UnlockedPolicies = new List<NationalPolicyDef>();
                // Re-add default policies if somehow missing, or handle logic for this.
                if (DefDatabase<NationalPolicyDef>.GetNamedSilentFail("NationalPolicy_Expansionist") != null) UnlockedPolicies.Add(DefDatabase<NationalPolicyDef>.GetNamed("NationalPolicy_Expansionist"));
                if (DefDatabase<NationalPolicyDef>.GetNamedSilentFail("NationalPolicy_Trader") != null) UnlockedPolicies.Add(DefDatabase<NationalPolicyDef>.GetNamed("NationalPolicy_Trader"));
            }
        }

        // NEW: Method to add an unlocked policy.
        public void AddUnlockedPolicy(NationalPolicyDef policy)
        {
            if (UnlockedPolicies != null && !UnlockedPolicies.Contains(policy))
            {
                UnlockedPolicies.Add(policy);
                Log.Message($"[NationalSystemMod] {Faction.Name} unlocked new policy: {policy.label}");
                if (Faction.IsPlayer) Messages.Message($"Your nation has unlocked a new policy: {policy.label}!", MessageTypeDefOf.PositiveEvent);
            }
        }

        // NEW: Method to adopt a policy
        public void AdoptPolicy(NationalPolicyDef policy)
        {
            if (policy != null && UnlockedPolicies.Contains(policy))
            {
                CurrentPolicy = policy;
                Log.Message($"[NationalSystemMod] {Faction.Name} adopted policy: {policy.label}");
            }
        }

        // NEW: Method to check if a policy is adopted
        public bool IsPolicyAdopted(string defName)
        {
            return CurrentPolicy?.defName == defName;
        }

        // NEW: Method to get adopted policies (for now just current policy)
        public List<NationalPolicyDef> GetAdoptedPolicies()
        {
            var adopted = new List<NationalPolicyDef>();
            if (CurrentPolicy != null)
            {
                adopted.Add(CurrentPolicy);
            }
            return adopted;
        }

        // NEW: Method to update resources
        public void UpdateResource(NationalResourceType resourceType, float amount)
        {
            Resources?.UpdateResource(resourceType, amount);
        }

        // NEW: Method to get resource amount
        public float GetResource(NationalResourceType resourceType)
        {
            return Resources?.GetResourceAmount(resourceType) ?? 0f;
        }

        public void RecalculateDerivedStats()
        {
            // Initial calculations remain.
            NationalWealth = Resources.GetResourceAmount(NationalResourceType.Silver) + 
                             (Resources.GetResourceAmount(NationalResourceType.Food) * 0.1f) +
                             (Resources.GetResourceAmount(NationalResourceType.Steel) * 0.5f) +
                             (Resources.GetResourceAmount(NationalResourceType.Components) * 2f);

            NationalWealth = Mathf.Clamp(NationalWealth, 0f, 1000000f);

            // Military power now incorporates current policy effects as well.
            // A more detailed calculation might be needed here, or apply policy multipliers in combat.
            // For now, this is a very basic "strength indicator".
        }

        // NEW: Calculates the total upkeep cost for all active armies.
        // Returns a dictionary of resource types and their total required amounts.
        public Dictionary<NationalResourceType, float> GetTotalArmyUpkeep()
        {
            var upkeep = new Dictionary<NationalResourceType, float>();
            var armies = Common.CoreLogic.DiplomacyTracker?.GetArmiesForFaction(this.Faction) ?? new List<World.WorldArmy>();
            
            foreach (var army in armies)
            {
                // These values should match what's in WorldArmy.ConsumeSupplies()
                float foodUpkeep = army.MilitaryStrength * 0.01f;
                float steelUpkeep = army.MilitaryStrength * 0.001f;
                
                if (!upkeep.ContainsKey(NationalResourceType.Food)) upkeep[NationalResourceType.Food] = 0;
                upkeep[NationalResourceType.Food] += foodUpkeep;

                if (!upkeep.ContainsKey(NationalResourceType.Steel)) upkeep[NationalResourceType.Steel] = 0;
                upkeep[NationalResourceType.Steel] += steelUpkeep;
            }
            return upkeep;
        }

        // NEW: Calculates the total military strength including garrisons and mobile armies.
        public float GetTotalMilitaryStrength()
        {
            float totalStrength = this.MilitaryPower; // Start with garrison strength.
            var armies = Common.CoreLogic.DiplomacyTracker?.GetArmiesForFaction(this.Faction) ?? new List<World.WorldArmy>();
            foreach (var army in armies)
            {
                totalStrength += army.MilitaryStrength;
            }
            return totalStrength;
        }
    }
}