using Verse;
using System.Collections.Generic;
using AIGen.NationalSystemMod.Defs;
using AIGen.NationalSystemMod.Economy; // NEW: For NationalResources.
using AIGen.NationalSystemMod.StrategicAI; // NEW: For StrategicGoal.
using UnityEngine;

namespace AIGen.NationalSystemMod.Nation
{
    public class NationalData : IExposable
    {
        public Faction Faction; 

        public float TotalInfluenceScore;
        public int ControlledTilesCount;

        public float NationalWealth; // General wealth/abstract resource.
        public float MilitaryPower;

        public NationalPolicyDef CurrentPolicy;

        // NEW: Faction's specific resource stockpiles.
        public NationalResources Resources;

        // NEW: Current strategic goal for AI factions.
        public StrategicGoal CurrentStrategicGoal;

        public NationalData() { }

        public NationalData(Faction faction)
        {
            this.Faction = faction;
            this.TotalInfluenceScore = 0f;
            this.ControlledTilesCount = 0;
            this.NationalWealth = 100f;
            this.MilitaryPower = 50f;
            this.CurrentPolicy = null;
            this.Resources = new NationalResources(); // Initialize resources.
            this.CurrentStrategicGoal = null; // No goal initially.
        }

        public bool IsValid()
        {
            return Faction != null && !Faction.Hidden && Faction.def.humanlikeFaction; // Policy can be null for player.
        }

        public void ExposeData()
        {
            Scribe_References.Look(ref Faction, "faction");
            Scribe_Values.Look(ref TotalInfluenceScore, "totalInfluenceScore", 0f);
            Scribe_Values.Look(ref ControlledTilesCount, "controlledTilesCount", 0);
            Scribe_Values.Look(ref NationalWealth, "nationalWealth", 100f);
            Scribe_Values.Look(ref MilitaryPower, "militaryPower", 50f);
            Scribe_Defs.Look(ref CurrentPolicy, "currentPolicy");
            
            // NEW: Scribe NationalResources.
            Scribe_Deep.Look(ref Resources, "resources");
            if (Scribe.mode == LoadSaveMode.PostLoadInit && Resources == null)
            {
                Resources = new NationalResources(); // Ensure it's initialized on load if missing.
            }

            // NEW: Scribe StrategicGoal.
            Scribe_Deep.Look(ref CurrentStrategicGoal, "currentStrategicGoal");

            // Add other data to be saved here as the mod expands.
        }

        public void RecalculateDerivedStats()
        {
            // Future: MilitaryPower could be influenced by resource availability.
            // For now, let's keep it simple.
            // Example: MilitaryPower += Resources.GetResourceAmount(NationalResourceType.Steel) / 50f;
            // NationalWealth could be total of Silver + (other resources * value).
            NationalWealth = Resources.GetResourceAmount(NationalResourceType.Silver) + 
                             (Resources.GetResourceAmount(NationalResourceType.Food) * 0.1f) +
                             (Resources.GetResourceAmount(NationalResourceType.Steel) * 0.5f) +
                             (Resources.GetResourceAmount(NationalResourceType.Components) * 2f);

            // Ensure wealth doesn't go below 0 or above a reasonable cap.
            NationalWealth = Mathf.Clamp(NationalWealth, 0f, 1000000f);
        }

        // NEW: Calculates the total upkeep cost for all active armies.
        // Returns a dictionary of resource types and their total required amounts.
        public Dictionary<NationalResourceType, float> GetTotalArmyUpkeep()
        {
            var upkeep = new Dictionary<NationalResourceType, float>();
            var armies = Common.CoreLogic.DiplomacyTracker?.GetArmiesForFaction(this.Faction) ?? new List<World.WorldArmy>();
            
            foreach (var army in armies)
            {
                // These values should match what's in WorldArmy.ConsumeSupplies()
                float foodUpkeep = army.MilitaryStrength * 0.01f;
                float steelUpkeep = army.MilitaryStrength * 0.001f;
                
                if (!upkeep.ContainsKey(NationalResourceType.Food)) upkeep[NationalResourceType.Food] = 0;
                upkeep[NationalResourceType.Food] += foodUpkeep;

                if (!upkeep.ContainsKey(NationalResourceType.Steel)) upkeep[NationalResourceType.Steel] = 0;
                upkeep[NationalResourceType.Steel] += steelUpkeep;
            }
            return upkeep;
        }

        // NEW: Calculates the total military strength including garrisons and mobile armies.
        public float GetTotalMilitaryStrength()
        {
            float totalStrength = this.MilitaryPower; // Start with garrison strength.
            var armies = Common.CoreLogic.DiplomacyTracker?.GetArmiesForFaction(this.Faction) ?? new List<World.WorldArmy>();
            foreach (var army in armies)
            {
                totalStrength += army.MilitaryStrength;
            }
            return totalStrength;
        }
    }
}