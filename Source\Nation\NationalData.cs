using Verse;
using System.Collections.Generic;
using AIGen.NationalSystemMod.Defs;
using AIGen.NationalSystemMod.Economy;
using AIGen.NationalSystemMod.StrategicAI;
using AIGen.NationalSystemMod.Research; // NEW: For NationalResearchManager.
using UnityEngine;
using RimWorld;

namespace AIGen.NationalSystemMod.Nation
{
    public class NationalData : IExposable
    {
        public Faction Faction; 

        public float TotalInfluenceScore;
        public int ControlledTilesCount;

        public float NationalWealth;
        public float MilitaryPower;

        public NationalPolicyDef CurrentPolicy;

        public NationalResources Resources;

        public StrategicGoal CurrentStrategicGoal;

        // NEW: National Research Manager.
        public NationalResearchManager Research;

        public NationalData() { }

        public NationalData(Faction faction)
        {
            this.Faction = faction;
            this.TotalInfluenceScore = 0f;
            this.ControlledTilesCount = 0;
            this.NationalWealth = 100f;
            this.MilitaryPower = 50f;
            this.CurrentPolicy = null;
            this.Resources = new NationalResources();
            this.CurrentStrategicGoal = null;
            this.Research = new NationalResearchManager(faction); // Initialize research manager.
        }

        public bool IsValid()
        {
            return Faction != null && !Faction.IsHidden && Faction.def.isCivil;
        }

        public void ExposeData()
        {
            Scribe_References.Look(ref Faction, "faction");
            Scribe_Values.Look(ref TotalInfluenceScore, "totalInfluenceScore", 0f);
            Scribe_Values.Look(ref ControlledTilesCount, "controlledTilesCount", 0);
            Scribe_Values.Look(ref NationalWealth, "nationalWealth", 100f);
            Scribe_Values.Look(ref MilitaryPower, "militaryPower", 50f);
            Scribe_Defs.Look(ref CurrentPolicy, "currentPolicy");
            
            Scribe_Deep.Look(ref Resources, "resources");
            if (Scribe.mode == LoadSaveMode.LoadingPostMapInit && Resources == null)
            {
                Resources = new NationalResources();
            }

            Scribe_Deep.Look(ref CurrentStrategicGoal, "currentStrategicGoal");

            // NEW: Scribe NationalResearchManager.
            Scribe_Deep.Look(ref Research, "research");
            if (Scribe.mode == LoadSaveMode.LoadingPostMapInit && Research == null)
            {
                Research = new NationalResearchManager(Faction); // Ensure it's initialized on load if missing.
            }
        }

        public void RecalculateDerivedStats()
        {
            // Initial calculations remain.
            NationalWealth = Resources.GetResourceAmount(NationalResourceType.Silver) + 
                             (Resources.GetResourceAmount(NationalResourceType.Food) * 0.1f) +
                             (Resources.GetResourceAmount(NationalResourceType.Steel) * 0.5f) +
                             (Resources.GetResourceAmount(NationalResourceType.Components) * 2f);

            NationalWealth = Mathf.Clamp(NationalWealth, 0f, 1000000f);

            // Apply military power bonus from research.
            // This is just the "flat" bonus. Dynamic effects are applied elsewhere.
            // A dedicated method would be better for this.
        }

        // NEW: Calculates the total upkeep cost for all active armies.
        // Returns a dictionary of resource types and their total required amounts.
        public Dictionary<NationalResourceType, float> GetTotalArmyUpkeep()
        {
            var upkeep = new Dictionary<NationalResourceType, float>();
            var armies = Common.CoreLogic.DiplomacyTracker?.GetArmiesForFaction(this.Faction) ?? new List<World.WorldArmy>();
            
            foreach (var army in armies)
            {
                // These values should match what's in WorldArmy.ConsumeSupplies()
                float foodUpkeep = army.MilitaryStrength * 0.01f;
                float steelUpkeep = army.MilitaryStrength * 0.001f;
                
                if (!upkeep.ContainsKey(NationalResourceType.Food)) upkeep[NationalResourceType.Food] = 0;
                upkeep[NationalResourceType.Food] += foodUpkeep;

                if (!upkeep.ContainsKey(NationalResourceType.Steel)) upkeep[NationalResourceType.Steel] = 0;
                upkeep[NationalResourceType.Steel] += steelUpkeep;
            }
            return upkeep;
        }

        // NEW: Calculates the total military strength including garrisons and mobile armies.
        public float GetTotalMilitaryStrength()
        {
            float totalStrength = this.MilitaryPower; // Start with garrison strength.
            var armies = Common.CoreLogic.DiplomacyTracker?.GetArmiesForFaction(this.Faction) ?? new List<World.WorldArmy>();
            foreach (var army in armies)
            {
                totalStrength += army.MilitaryStrength;
            }
            return totalStrength;
        }
    }
}