using Verse; // Core RimWorld namespace for basic utilities and logging.
using RimWorld; // For Faction and other RimWorld types
using RimWorld.Planet; // Needed for World and settlements.
using UnityEngine; // For Mathf and other Unity utilities
using System.Collections.Generic; // Useful for collections, though not heavily used here yet.
using System.Linq; // For LINQ queries, use sparingly if performance critical.
using AIGen.NationalSystemMod.World; // Add this using directive.
using AIGen.NationalSystemMod.Nation; // NEW: Add this using directive.
using AIGen.NationalSystemMod.Defs; // NEW: Add this for NationalPolicyDef.
using AIGen.NationalSystemMod.Economy; // NEW: For NationalResourceType.
using AIGen.NationalSystemMod.StrategicAI; // NEW: For StrategicGoalType.

namespace AIGen.NationalSystemMod.Common // IMPORTANT: Use your actual package ID and include .Common.
{
    // This class is static because it will hold global logic and won't need instances.
    public static class CoreLogic 
    {
        // A simple flag to track if our mod's main systems have been initialized.
        private static bool _isModSystemInitialized = false;

        // Public property to check the initialization status from anywhere in the mod.
        public static bool IsModSystemInitialized => _isModSystemInitialized;

        // NEW: Instance of our WorldInfluenceMap to manage influence data globally.
        public static World.WorldInfluenceMap WorldInfluenceMap { get; private set; }

        // NEW: Reference to the diplomacy tracker.
        public static World.FactionDiplomacyTracker DiplomacyTracker { get; private set; }

        // NEW: Dictionary to store NationalData for each Faction.
        // This will be managed by a GameComponent for persistence.
        private static Dictionary<Faction, NationalData> _nationalDataByFaction;
        public static Dictionary<Faction, NationalData> NationalDataByFaction => _nationalDataByFaction;

        // NEW: Tick for resource production.
        private const int RESOURCE_PRODUCTION_INTERVAL_TICKS = GenDate.TicksPerDay; // Daily production.
        private static int _lastResourceProductionTick = -1;

        // NEW: Tick for AI strategic goal evaluation.
        private const int AI_GOAL_EVALUATION_INTERVAL_TICKS = GenDate.TicksPerDay * 10; // Every 10 days, AI re-evaluates goals.
        private static int _lastAIGoalEvaluationTick = -1;

        // NEW: Internal GameComponent for CoreLogic's persistent data (like NationalData).
        // This will allow us to save/load the _nationalDataByFaction dictionary.
        public class CoreLogicGameComponent : GameComponent
        {
            private bool _hasInitialized = false;

            public CoreLogicGameComponent() { } // GameComponent constructor

            public override void GameComponentTick()
            {
                base.GameComponentTick();

                // Initialize systems once the game is fully loaded
                if (!_hasInitialized && Find.TickManager != null && Find.TickManager.TicksGame > 10)
                {
                    try
                    {
                        InitializeModSystems();
                        _hasInitialized = true;
                    }
                    catch (System.Exception ex)
                    {
                        Log.Error($"[NationalSystemMod] Failed to initialize mod systems: {ex}");
                    }
                }

                // Only run periodic updates after initialization
                if (_hasInitialized && GenTicks.TicksGame % 250 == 0) // Every ~4 seconds
                {
                    try
                    {
                        PeriodicUpdate();
                    }
                    catch (System.Exception ex)
                    {
                        Log.Error($"[NationalSystemMod] Error in periodic update: {ex}");
                    }
                }
            }

            public override void ExposeData()
            {
                base.ExposeData();
                // Scribe _nationalDataByFaction using LookMode.Reference for Faction and LookMode.Deep for NationalData.
                Scribe_Collections.Look(ref _nationalDataByFaction, "nationalDataByFaction", LookMode.Reference, LookMode.Deep);

                // If loading from an old save where _nationalDataByFaction was null, initialize it.
                if (Scribe.mode == LoadSaveMode.PostLoadInit && _nationalDataByFaction == null)
                {
                    _nationalDataByFaction = new Dictionary<Faction, NationalData>();
                }
            }
        }

        // This method will be called once to perform major, one-time setup tasks for the mod's systems.
        // It should ideally be invoked after the game world has fully loaded to ensure all necessary
        // game data is available.
        public static void InitializeModSystems()
        {
            if (_isModSystemInitialized) // Prevent re-initialization if already done.
            {
                return;
            }

            // Safety checks to ensure game is properly loaded
            if (Current.Game == null || Find.World == null || Find.FactionManager == null)
            {
                Log.Warning("[NationalSystemMod.CoreLogic] Game not fully loaded yet, deferring initialization.");
                return;
            }

            Log.Message("[NationalSystemMod.CoreLogic] Core systems initializing...");

            // NEW: Initialize the WorldInfluenceMap instance.
            WorldInfluenceMap = Current.Game.GetComponent<World.WorldInfluenceMap>();
            if (WorldInfluenceMap == null)
            {
                WorldInfluenceMap = new World.WorldInfluenceMap();
                Current.Game.components.Add(WorldInfluenceMap);
            }

            // NEW: Get or add FactionDiplomacyTracker.
            DiplomacyTracker = Find.World.GetComponent<World.FactionDiplomacyTracker>();
            if (DiplomacyTracker == null)
            {
                DiplomacyTracker = new World.FactionDiplomacyTracker(Find.World);
                Find.World.components.Add(DiplomacyTracker);
            }

            // NEW: Get or add CoreLogic's own GameComponent for persistent data.
            var coreLogicComp = Current.Game.GetComponent<CoreLogicGameComponent>();
            if (coreLogicComp == null)
            {
                Current.Game.components.Add(new CoreLogicGameComponent());
            }
            
            // Ensure NationalData exists for all relevant factions at game start.
            // This covers new games and factions appearing during gameplay.
            EnsureNationalDataExistsForFactions();

            // NEW: Assign policies if not already assigned (for new games or old saves).
            AssignPoliciesToFactions();

            // NEW: Perform initial influence calculation after game world has loaded.
            // This should only happen once per game session.
            CalculateInitialWorldInfluence();
            //NationalBorderDrawer.MarkCacheDirty(); // NEW: Mark border cache dirty after initial influence calculation.

            _isModSystemInitialized = true; // Mark as initialized after setup.
            Log.Message("[NationalSystemMod.CoreLogic] Core systems initialized.");
        }

        // NEW: Ensure NationalData exists for all playable/relevant factions.
        private static void EnsureNationalDataExistsForFactions()
        {
            if (_nationalDataByFaction == null)
            {
                _nationalDataByFaction = new Dictionary<Faction, NationalData>();
            }

            foreach (Faction faction in Find.FactionManager.AllFactions)
            {
                if (faction.def.permanentEnemy || faction.Hidden || faction.IsPlayer) continue;

                if (!_nationalDataByFaction.ContainsKey(faction))
                {
                    _nationalDataByFaction.Add(faction, new NationalData(faction));
                    Log.Message($"[NationalSystemMod.CoreLogic] Created NationalData for faction: {faction.Name}");
                }
            }
        }

        // NEW: Assign NationalPolicies to factions.
        private static void AssignPoliciesToFactions()
        {
            List<NationalPolicyDef> allPolicies = DefDatabase<NationalPolicyDef>.AllDefsListForReading;
            if (allPolicies.Count == 0)
            {
                Log.Warning("[NationalSystemMod.CoreLogic] No NationalPolicyDefs found in DefDatabase! AI behavior will be default.");
                return;
            }

            if (_nationalDataByFaction == null) return;
            foreach (var entry in _nationalDataByFaction)
            {
                Faction faction = entry.Key;
                NationalData nationalData = entry.Value;
                
                if (faction.IsPlayer)
                {
                    continue; 
                }

                if (nationalData.CurrentPolicy == null) 
                {
                    nationalData.CurrentPolicy = allPolicies.RandomElement();
                    Log.Message($"[NationalSystemMod.CoreLogic] Assigned policy '{nationalData.CurrentPolicy.label}' to {faction.Name}.");
                }
            }
        }

        // --- NEW METHOD: CalculateInitialWorldInfluence() ---
        // This method will iterate through existing settlements and assign them basic influence.
        // This is a one-time calculation during mod initialization, NOT a tick-based one.
        private static void CalculateInitialWorldInfluence()
        {
            Log.Message("[NationalSystemMod.CoreLogic] Calculating initial world influence...");

            // Iterate through all settlements on the world map.
            // Ensure this is optimized for potentially many settlements.
            foreach (Settlement settlement in Find.WorldObjects.Settlements)
            {
                // We only care about settlements owned by factions. Player colonies are settlements too.
                if (settlement.Faction != null)
                {
                    // For initial setup, let's give them influence over their own tile
                    // and a few adjacent tiles. This can be refined later.
                    float baseInfluence = 0.5f; // A simple base influence value.

                    // Influence the settlement's own tile
                    WorldInfluenceMap.SetInfluenceData(settlement.Tile, settlement.Faction, baseInfluence);

                    // Influence adjacent tiles - simplified for now
                    // TODO: Implement proper adjacent tile calculation

                    // TODO: Add adjacent tile influence when proper API is available
                }
            }
            Log.Message("[NationalSystemMod.CoreLogic] Initial world influence calculation complete.");
            // NationalBorderDrawer.MarkCacheDirty(); // Commented out to prevent initialization issues
        }

        // This method is a placeholder for periodic updates, like daily or weekly calculations.
        // It should NOT be called every game tick. This is crucial for performance.
        // Logic for things like daily influence changes, economic summaries, or long-term diplomatic shifts can go here.
        public static void PeriodicUpdate()
        {
            if (!IsModSystemInitialized) return;

            InfluenceManager.UpdateInfluence(); 

            // AI decision-making (war, trade)
            if (GenTicks.TicksGame % (GenDate.TicksPerDay * 3) == 0) 
            {
                if (DiplomacyTracker != null)
                {
                    DiplomacyTracker.AIDeclareWarLogic();
                    DiplomacyTracker.AIGenerateTradeOffers();
                    DiplomacyTracker.AIEvaluateTradeOffers();
                }
            }

            if (GenTicks.TicksGame >= _lastResourceProductionTick + RESOURCE_PRODUCTION_INTERVAL_TICKS)
            {
                ProduceNationalResources();
                _lastResourceProductionTick = GenTicks.TicksGame;
            }

            // NEW: AI strategic goal evaluation.
            if (GenTicks.TicksGame >= _lastAIGoalEvaluationTick + AI_GOAL_EVALUATION_INTERVAL_TICKS)
            {
                EvaluateAIStrategicGoals();
                _lastAIGoalEvaluationTick = GenTicks.TicksGame;
            }

            // Recalculate derived stats like wealth based on new resources.
            foreach (NationalData data in _nationalDataByFaction.Values)
            {
                data.RecalculateDerivedStats();
            }
        }

        // NEW: Method to handle resource production for all factions.
        private static void ProduceNationalResources()
        {
            if (_nationalDataByFaction == null) return;
            foreach (var entry in _nationalDataByFaction)
            {
                Faction faction = entry.Key;
                NationalData nationalData = entry.Value;

                if (nationalData == null || !nationalData.IsValid()) continue;

                // Production from controlled tiles.
                List<int> ownedTiles = WorldInfluenceMap.InfluenceDataByTile
                    .Where(pair => pair.Value.faction == faction)
                    .Select(pair => pair.Key)
                    .ToList();

                foreach (int tileID in ownedTiles)
                {
                    InfluenceTileData tileData = WorldInfluenceMap.GetInfluenceData(tileID);
                    if (tileData?.ResourceOutput != null)
                    {
                        foreach (var resEntry in tileData.ResourceOutput)
                        {
                            nationalData.Resources.AddResource(resEntry.Key, resEntry.Value);
                        }
                    }
                }

                // Add base silver production / economic activity.
                nationalData.Resources.AddResource(NationalResourceType.Silver, 10f + (nationalData.ControlledTilesCount * 0.5f) + (nationalData.NationalWealth * 0.01f));

                // Simulate resource consumption (e.g., food for population, steel for military upkeep).
                nationalData.Resources.TryConsumeResource(NationalResourceType.Food, 10f); // Base consumption.
                nationalData.Resources.TryConsumeResource(NationalResourceType.Steel, 2f); // Military upkeep.
                nationalData.Resources.TryConsumeResource(NationalResourceType.Components, 0.5f); // Tech upkeep.

                // Check for resource shortages and apply penalties.
                if (nationalData.Resources.GetResourceAmount(NationalResourceType.Food) < 0)
                {
                    nationalData.MilitaryPower = Mathf.Max(0, nationalData.MilitaryPower - 1f); // Reduced military.
                    nationalData.NationalWealth = Mathf.Max(0, nationalData.NationalWealth - 5f); // Economic hit.
                    if (faction.IsPlayer) Messages.Message($"{faction.Name} is suffering from a food shortage!", MessageTypeDefOf.NegativeEvent);
                }
                // Add more penalties for other resource types.
            }
        }

        // NEW: Method for AI factions to evaluate and set strategic goals.
        private static void EvaluateAIStrategicGoals()
        {
            if (_nationalDataByFaction == null) return;
            List<NationalResourceType> allResources = System.Enum.GetValues(typeof(NationalResourceType)).Cast<NationalResourceType>().ToList();

            foreach (var entry in _nationalDataByFaction)
            {
                Faction faction = entry.Key;
                NationalData nationalData = entry.Value;

                if (nationalData == null || !nationalData.IsValid() || faction.IsPlayer) continue; // Only AI.

                // If current goal expired or no goal, choose a new one.
                if (nationalData.CurrentStrategicGoal == null || !nationalData.CurrentStrategicGoal.IsActive)
                {
                    AIStrategicGoalType newGoalType = AIStrategicGoalType.None;
                    NationalResourceType desiredResource = NationalResourceType.None;
                    Faction targetFaction = null;
                    int durationDays = Rand.Range(30, 90); // Goal lasts 30-90 days.

                    // Simple AI logic to pick a goal:
                    // 1. Check for critical resource shortages.
                    // 2. Assess military power relative to neighbors (for supremacy/expansion).
                    // 3. Consider current wealth for economic prosperity.

                    // Prioritize resource shortage.
                    if (nationalData.Resources.GetResourceAmount(NationalResourceType.Food) < 50)
                    {
                        newGoalType = AIStrategicGoalType.ResourceAcquisition;
                        desiredResource = NationalResourceType.Food;
                    }
                    else if (nationalData.Resources.GetResourceAmount(NationalResourceType.Steel) < 100)
                    {
                        newGoalType = AIStrategicGoalType.ResourceAcquisition;
                        desiredResource = NationalResourceType.Steel;
                    }
                    else if (nationalData.Resources.GetResourceAmount(NationalResourceType.Components) < 20)
                    {
                        newGoalType = AIStrategicGoalType.ResourceAcquisition;
                        desiredResource = NationalResourceType.Components;
                    }
                    else
                    {
                        // No critical shortage, choose based on policy and general situation.
                        float expansionScore = nationalData.CurrentPolicy.influenceSpreadModifier;
                        float militaryScore = nationalData.CurrentPolicy.militaryAggressionFactor;
                        float economicScore = nationalData.CurrentPolicy.tradeInterestFactor;

                        // Add situational modifiers.
                        if (nationalData.ControlledTilesCount < Find.WorldGrid.TilesCount / 20) expansionScore *= 1.5f; // Small factions want to grow.
                        if (nationalData.MilitaryPower < 100f) militaryScore *= 1.2f; // Weak factions want to build military.
                        if (nationalData.NationalWealth < 5000f) economicScore *= 1.3f; // Poor factions want wealth.

                        // If at war, prioritize military.
                        if (Find.FactionManager.AllFactionsListForReading.Any(f => f != faction && DiplomacyTracker.GetDiplomaticState(faction, f) == DiplomaticState.AtWar))
                        {
                            militaryScore *= 2.0f; // High priority for military during war.
                        }

                        // Pick the highest score, weighted randomly.
                        float totalScore = expansionScore + militaryScore + economicScore;
                        float randomRoll = Rand.Value * totalScore;

                        if (randomRoll < expansionScore)
                        {
                            newGoalType = AIStrategicGoalType.TerritorialExpansion;
                        }
                        else if (randomRoll < expansionScore + militaryScore)
                        {
                            newGoalType = AIStrategicGoalType.MilitarySupremacy;
                            // Optionally pick a rival as target.
                            targetFaction = Find.FactionManager.AllFactionsListForReading
                                .Where(f => f != faction && !f.IsPlayer && !f.Hidden && DiplomacyTracker.GetDiplomaticState(faction, f) == DiplomaticState.Rival)
                                .RandomElement();
                             if (targetFaction == null) targetFaction = Find.FactionManager.AllFactionsListForReading.Where(f => f != faction && !f.IsPlayer && !f.Hidden).RandomElement();
                        }
                        else
                        {
                            newGoalType = AIStrategicGoalType.EconomicProsperity;
                        }
                    }

                    nationalData.CurrentStrategicGoal = new StrategicGoal(newGoalType, durationDays, targetFaction, desiredResource);
                    Log.Message($"[NationalSystemMod.AI] {faction.Name} adopted a new strategic goal: {nationalData.CurrentStrategicGoal.GetGoalDescription()}");
                }
            }
        }

        // NEW: Method to create and spawn a new faction army.
        public static WorldArmy CreateFactionArmy(Faction faction, int originTile, float strength)
        {
            if (faction == null || !CoreLogic.NationalDataByFaction.ContainsKey(faction))
            {
                Log.Warning($"[NationalSystemMod] Tried to create an army for a faction with no NationalData: {faction?.Name ?? "NULL"}");
                return null;
            }

            WorldArmy army = (WorldArmy)WorldObjectMaker.MakeWorldObject(NationalSystemDefOf.NationalSystem_WorldArmy);
            army.Setup(faction, originTile, strength);
            
            Find.WorldObjects.Add(army);
            DiplomacyTracker?.AddArmy(army);

            Log.Message($"[NationalSystemMod] Created army for {faction.Name} at tile {originTile} with strength {strength:F0}.");
            return army;
        }

        // --- FUTURE ADDITIONS ---
        // Other event-driven methods or global utility functions can be added here as needed.
        // Examples: OnColonyAttacked, OnFactionDefeated, OnPawnDied.
    }
} 