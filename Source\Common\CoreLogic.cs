using Verse; // Core RimWorld namespace for basic utilities and logging.
using RimWorld; // For Faction and other RimWorld types
using RimWorld.Planet; // Needed for World and settlements.
using UnityEngine; // For Mathf and other Unity utilities
using System.Collections.Generic; // Useful for collections, though not heavily used here yet.
using System.Linq; // For LINQ queries, use sparingly if performance critical.
using AIGen.NationalSystemMod.World; // Add this using directive.
using AIGen.NationalSystemMod.Nation; // NEW: Add this using directive.
using AIGen.NationalSystemMod.Defs; // NEW: Add this for NationalPolicyDef.
using AIGen.NationalSystemMod.Economy; // NEW: For NationalResourceType.
using AIGen.NationalSystemMod.StrategicAI; // NEW: For StrategicGoalType.

namespace AIGen.NationalSystemMod.Common // IMPORTANT: Use your actual package ID and include .Common.
{
    // This class is static because it will hold global logic and won't need instances.
    public static class CoreLogic 
    {
        // A simple flag to track if our mod's main systems have been initialized.
        private static bool _isModSystemInitialized = false;

        // Public property to check the initialization status from anywhere in the mod.
        public static bool IsModSystemInitialized => _isModSystemInitialized;

        // NEW: Instance of our WorldInfluenceMap to manage influence data globally.
        public static World.WorldInfluenceMap WorldInfluenceMap { get; private set; }

        // NEW: Reference to the diplomacy tracker.
        public static World.FactionDiplomacyTracker DiplomacyTracker { get; private set; }

        // NEW: Dictionary to store NationalData for each Faction.
        // This will be managed by a GameComponent for persistence.
        private static Dictionary<Faction, NationalData> _nationalDataByFaction;
        public static Dictionary<Faction, NationalData> NationalDataByFaction => _nationalDataByFaction;

        // NEW: Tick for resource production.
        private const int RESOURCE_PRODUCTION_INTERVAL_TICKS = GenDate.TicksPerDay; // Daily production.
        private static int _lastResourceProductionTick = -1;

        // NEW: Tick for AI strategic goal evaluation.
        private const int AI_GOAL_EVALUATION_INTERVAL_TICKS = GenDate.TicksPerDay * 10; // Every 10 days, AI re-evaluates goals.
        private static int _lastAIGoalEvaluationTick = -1;

        // NEW: Internal GameComponent for CoreLogic's persistent data (like NationalData).
        // This will allow us to save/load the _nationalDataByFaction dictionary.
        public class CoreLogicGameComponent : GameComponent
        {
            private bool _hasInitialized = false;

            public CoreLogicGameComponent() { } // GameComponent constructor

            public override void GameComponentTick()
            {
                base.GameComponentTick();

                // Initialize systems once the game is fully loaded
                if (!_hasInitialized && Find.TickManager != null && Find.TickManager.TicksGame > 100)
                {
                    // Additional safety checks before initialization
                    if (Current.Game != null && Find.World != null && Find.FactionManager != null &&
                        Find.WorldObjects != null && Find.WorldGrid != null &&
                        DefDatabase<NationalPolicyDef>.DefCount > 0) // Ensure DefDatabase is loaded
                    {
                        try
                        {
                            InitializeModSystems();
                            _hasInitialized = true;
                        }
                        catch (System.Exception ex)
                        {
                            Log.Error($"[NationalSystemMod] Failed to initialize mod systems: {ex}");
                        }
                    }
                }

                // Only run periodic updates after initialization
                if (_hasInitialized && IsModSystemInitialized && GenTicks.TicksGame % 250 == 0) // Every ~4 seconds
                {
                    try
                    {
                        PeriodicUpdate();
                    }
                    catch (System.Exception ex)
                    {
                        Log.Error($"[NationalSystemMod] Error in periodic update: {ex}");
                    }
                }
            }

            public override void ExposeData()
            {
                base.ExposeData();
                // Scribe _nationalDataByFaction using LookMode.Reference for Faction and LookMode.Deep for NationalData.
                Scribe_Collections.Look(ref _nationalDataByFaction, "nationalDataByFaction", LookMode.Reference, LookMode.Deep);

                // If loading from an old save where _nationalDataByFaction was null, initialize it.
                if (Scribe.mode == LoadSaveMode.PostLoadInit && _nationalDataByFaction == null)
                {
                    _nationalDataByFaction = new Dictionary<Faction, NationalData>();
                }
            }
        }

        // This method will be called once to perform major, one-time setup tasks for the mod's systems.
        // It should ideally be invoked after the game world has fully loaded to ensure all necessary
        // game data is available.
        public static void InitializeModSystems()
        {
            if (_isModSystemInitialized) // Prevent re-initialization if already done.
            {
                return;
            }

            try
            {
                // Safety checks to ensure game is properly loaded
                if (Current.Game == null)
                {
                    Log.Warning("[NationalSystemMod.CoreLogic] Current.Game is null, deferring initialization.");
                    return;
                }

                if (Find.World == null)
                {
                    Log.Warning("[NationalSystemMod.CoreLogic] Find.World is null, deferring initialization.");
                    return;
                }

                if (Find.FactionManager == null)
                {
                    Log.Warning("[NationalSystemMod.CoreLogic] Find.FactionManager is null, deferring initialization.");
                    return;
                }

                if (Find.WorldObjects == null)
                {
                    Log.Warning("[NationalSystemMod.CoreLogic] Find.WorldObjects is null, deferring initialization.");
                    return;
                }

                Log.Message("[NationalSystemMod.CoreLogic] Core systems initializing...");

                // NEW: Initialize the WorldInfluenceMap instance.
                try
                {
                    WorldInfluenceMap = Current.Game.GetComponent<World.WorldInfluenceMap>();
                    if (WorldInfluenceMap == null)
                    {
                        WorldInfluenceMap = new World.WorldInfluenceMap();
                        Current.Game.components.Add(WorldInfluenceMap);
                        Log.Message("[NationalSystemMod.CoreLogic] Created new WorldInfluenceMap.");
                    }
                    else
                    {
                        Log.Message("[NationalSystemMod.CoreLogic] Found existing WorldInfluenceMap.");
                    }
                }
                catch (System.Exception ex)
                {
                    Log.Error($"[NationalSystemMod.CoreLogic] Failed to initialize WorldInfluenceMap: {ex}");
                    return;
                }

                // NEW: Get or add FactionDiplomacyTracker.
                try
                {
                    DiplomacyTracker = Find.World.GetComponent<World.FactionDiplomacyTracker>();
                    if (DiplomacyTracker == null)
                    {
                        DiplomacyTracker = new World.FactionDiplomacyTracker(Find.World);
                        Find.World.components.Add(DiplomacyTracker);
                        Log.Message("[NationalSystemMod.CoreLogic] Created new FactionDiplomacyTracker.");
                    }
                    else
                    {
                        Log.Message("[NationalSystemMod.CoreLogic] Found existing FactionDiplomacyTracker.");
                    }
                }
                catch (System.Exception ex)
                {
                    Log.Error($"[NationalSystemMod.CoreLogic] Failed to initialize FactionDiplomacyTracker: {ex}");
                    return;
                }

                // NEW: Get or add CoreLogic's own GameComponent for persistent data.
                try
                {
                    var coreLogicComp = Current.Game.GetComponent<CoreLogicGameComponent>();
                    if (coreLogicComp == null)
                    {
                        Current.Game.components.Add(new CoreLogicGameComponent());
                        Log.Message("[NationalSystemMod.CoreLogic] Created new CoreLogicGameComponent.");
                    }
                }
                catch (System.Exception ex)
                {
                    Log.Error($"[NationalSystemMod.CoreLogic] Failed to initialize CoreLogicGameComponent: {ex}");
                    return;
                }

                // Ensure NationalData exists for all relevant factions at game start.
                // This covers new games and factions appearing during gameplay.
                try
                {
                    EnsureNationalDataExistsForFactions();
                }
                catch (System.Exception ex)
                {
                    Log.Error($"[NationalSystemMod.CoreLogic] Failed to ensure NationalData for factions: {ex}");
                    return;
                }

                // NEW: Assign policies if not already assigned (for new games or old saves).
                try
                {
                    AssignPoliciesToFactions();
                }
                catch (System.Exception ex)
                {
                    Log.Error($"[NationalSystemMod.CoreLogic] Failed to assign policies to factions: {ex}");
                    return;
                }

                // NEW: Perform initial influence calculation after game world has loaded.
                // This should only happen once per game session.
                try
                {
                    CalculateInitialWorldInfluence();
                }
                catch (System.Exception ex)
                {
                    Log.Error($"[NationalSystemMod.CoreLogic] Failed to calculate initial world influence: {ex}");
                    return;
                }

                _isModSystemInitialized = true; // Mark as initialized after setup.
                Log.Message("[NationalSystemMod.CoreLogic] Core systems initialized successfully.");
            }
            catch (System.Exception ex)
            {
                Log.Error($"[NationalSystemMod.CoreLogic] Critical error during initialization: {ex}");
            }
        }

        // NEW: Ensure NationalData exists for all playable/relevant factions.
        private static void EnsureNationalDataExistsForFactions()
        {
            if (_nationalDataByFaction == null)
            {
                _nationalDataByFaction = new Dictionary<Faction, NationalData>();
            }

            if (Find.FactionManager?.AllFactions == null)
            {
                Log.Warning("[NationalSystemMod.CoreLogic] FactionManager.AllFactions is null, skipping faction data creation.");
                return;
            }

            foreach (Faction faction in Find.FactionManager.AllFactions)
            {
                if (faction == null || faction.def == null) continue;
                if (faction.def.permanentEnemy || faction.Hidden || faction.IsPlayer) continue;

                if (!_nationalDataByFaction.ContainsKey(faction))
                {
                    try
                    {
                        _nationalDataByFaction.Add(faction, new NationalData(faction));
                        Log.Message($"[NationalSystemMod.CoreLogic] Created NationalData for faction: {faction.Name ?? "Unknown"}");
                    }
                    catch (System.Exception ex)
                    {
                        Log.Error($"[NationalSystemMod.CoreLogic] Failed to create NationalData for faction {faction.Name ?? "Unknown"}: {ex}");
                    }
                }
            }
        }

        // NEW: Assign NationalPolicies to factions.
        private static void AssignPoliciesToFactions()
        {
            try
            {
                // Check if DefDatabase is ready
                if (DefDatabase<NationalPolicyDef>.DefCount == 0)
                {
                    Log.Warning("[NationalSystemMod.CoreLogic] DefDatabase not ready yet, skipping policy assignment.");
                    return;
                }

                List<NationalPolicyDef> allPolicies = DefDatabase<NationalPolicyDef>.AllDefsListForReading;
                if (allPolicies == null || allPolicies.Count == 0)
                {
                    Log.Warning("[NationalSystemMod.CoreLogic] No NationalPolicyDefs found in DefDatabase! AI behavior will be default.");
                    return;
                }

                Log.Message($"[NationalSystemMod.CoreLogic] Found {allPolicies.Count} policy definitions.");

                if (_nationalDataByFaction == null)
                {
                    Log.Warning("[NationalSystemMod.CoreLogic] _nationalDataByFaction is null, cannot assign policies.");
                    return;
                }

                foreach (var entry in _nationalDataByFaction)
                {
                    Faction faction = entry.Key;
                    NationalData nationalData = entry.Value;

                    if (faction == null || nationalData == null) continue;
                    if (faction.IsPlayer) continue;

                    if (nationalData.CurrentPolicy == null)
                    {
                        try
                        {
                            nationalData.CurrentPolicy = allPolicies.RandomElement();
                            Log.Message($"[NationalSystemMod.CoreLogic] Assigned policy '{nationalData.CurrentPolicy?.label ?? "Unknown"}' to {faction.Name ?? "Unknown"}.");
                        }
                        catch (System.Exception ex)
                        {
                            Log.Error($"[NationalSystemMod.CoreLogic] Failed to assign policy to faction {faction.Name ?? "Unknown"}: {ex}");
                        }
                    }
                }
            }
            catch (System.Exception ex)
            {
                Log.Error($"[NationalSystemMod.CoreLogic] Error in AssignPoliciesToFactions: {ex}");
            }
        }

        // --- NEW METHOD: CalculateInitialWorldInfluence() ---
        // This method will iterate through existing settlements and assign them basic influence.
        // This is a one-time calculation during mod initialization, NOT a tick-based one.
        private static void CalculateInitialWorldInfluence()
        {
            try
            {
                Log.Message("[NationalSystemMod.CoreLogic] Calculating initial world influence...");

                if (Find.WorldObjects?.Settlements == null)
                {
                    Log.Warning("[NationalSystemMod.CoreLogic] Find.WorldObjects.Settlements is null, skipping influence calculation.");
                    return;
                }

                if (WorldInfluenceMap == null)
                {
                    Log.Warning("[NationalSystemMod.CoreLogic] WorldInfluenceMap is null, skipping influence calculation.");
                    return;
                }

                // Iterate through all settlements on the world map.
                // Ensure this is optimized for potentially many settlements.
                foreach (Settlement settlement in Find.WorldObjects.Settlements)
                {
                    if (settlement?.Faction == null) continue;

                    try
                    {
                        // For initial setup, let's give them influence over their own tile
                        // and a few adjacent tiles. This can be refined later.
                        float baseInfluence = 0.5f; // A simple base influence value.

                        // Influence the settlement's own tile
                        WorldInfluenceMap.SetInfluenceData(settlement.Tile, settlement.Faction, baseInfluence);

                        // Influence adjacent tiles - simplified for now
                        // TODO: Implement proper adjacent tile calculation

                        // TODO: Add adjacent tile influence when proper API is available
                    }
                    catch (System.Exception ex)
                    {
                        Log.Error($"[NationalSystemMod.CoreLogic] Failed to set influence for settlement {settlement.Label ?? "Unknown"}: {ex}");
                    }
                }
                Log.Message("[NationalSystemMod.CoreLogic] Initial world influence calculation complete.");
                // NationalBorderDrawer.MarkCacheDirty(); // Commented out to prevent initialization issues
            }
            catch (System.Exception ex)
            {
                Log.Error($"[NationalSystemMod.CoreLogic] Error in CalculateInitialWorldInfluence: {ex}");
            }
        }

        // This method is a placeholder for periodic updates, like daily or weekly calculations.
        // It should NOT be called every game tick. This is crucial for performance.
        // Logic for things like daily influence changes, economic summaries, or long-term diplomatic shifts can go here.
        public static void PeriodicUpdate()
        {
            if (!IsModSystemInitialized) return;

            InfluenceManager.UpdateInfluence(); 

            // AI decision-making (war, trade)
            if (GenTicks.TicksGame % (GenDate.TicksPerDay * 3) == 0) 
            {
                if (DiplomacyTracker != null)
                {
                    DiplomacyTracker.AIDeclareWarLogic();
                    DiplomacyTracker.AIGenerateTradeOffers();
                    DiplomacyTracker.AIEvaluateTradeOffers();
                }
            }

            if (GenTicks.TicksGame >= _lastResourceProductionTick + RESOURCE_PRODUCTION_INTERVAL_TICKS)
            {
                ProduceNationalResources();
                _lastResourceProductionTick = GenTicks.TicksGame;
            }

            // NEW: AI strategic goal evaluation.
            if (GenTicks.TicksGame >= _lastAIGoalEvaluationTick + AI_GOAL_EVALUATION_INTERVAL_TICKS)
            {
                EvaluateAIStrategicGoals();
                _lastAIGoalEvaluationTick = GenTicks.TicksGame;
            }

            // Recalculate derived stats like wealth based on new resources.
            foreach (NationalData data in _nationalDataByFaction.Values)
            {
                data.RecalculateDerivedStats();
            }
        }

        // NEW: Method to handle resource production for all factions.
        private static void ProduceNationalResources()
        {
            if (_nationalDataByFaction == null) return;
            foreach (var entry in _nationalDataByFaction)
            {
                Faction faction = entry.Key;
                NationalData nationalData = entry.Value;

                if (nationalData == null || !nationalData.IsValid()) continue;

                // Production from controlled tiles.
                List<int> ownedTiles = WorldInfluenceMap.InfluenceDataByTile
                    .Where(pair => pair.Value.faction == faction)
                    .Select(pair => pair.Key)
                    .ToList();

                foreach (int tileID in ownedTiles)
                {
                    InfluenceTileData tileData = WorldInfluenceMap.GetInfluenceData(tileID);
                    if (tileData?.ResourceOutput != null)
                    {
                        foreach (var resEntry in tileData.ResourceOutput)
                        {
                            nationalData.Resources.AddResource(resEntry.Key, resEntry.Value);
                        }
                    }
                }

                // Add base silver production / economic activity.
                nationalData.Resources.AddResource(NationalResourceType.Silver, 10f + (nationalData.ControlledTilesCount * 0.5f) + (nationalData.NationalWealth * 0.01f));

                // Simulate resource consumption (e.g., food for population, steel for military upkeep).
                nationalData.Resources.TryConsumeResource(NationalResourceType.Food, 10f); // Base consumption.
                nationalData.Resources.TryConsumeResource(NationalResourceType.Steel, 2f); // Military upkeep.
                nationalData.Resources.TryConsumeResource(NationalResourceType.Components, 0.5f); // Tech upkeep.

                // Check for resource shortages and apply penalties.
                if (nationalData.Resources.GetResourceAmount(NationalResourceType.Food) < 0)
                {
                    nationalData.MilitaryPower = Mathf.Max(0, nationalData.MilitaryPower - 1f); // Reduced military.
                    nationalData.NationalWealth = Mathf.Max(0, nationalData.NationalWealth - 5f); // Economic hit.
                    if (faction.IsPlayer) Messages.Message($"{faction.Name} is suffering from a food shortage!", MessageTypeDefOf.NegativeEvent);
                }
                // Add more penalties for other resource types.
            }
        }

        // NEW: Method for AI factions to evaluate and set strategic goals.
        private static void EvaluateAIStrategicGoals()
        {
            if (_nationalDataByFaction == null) return;
            List<NationalResourceType> allResources = System.Enum.GetValues(typeof(NationalResourceType)).Cast<NationalResourceType>().ToList();

            foreach (var entry in _nationalDataByFaction)
            {
                Faction faction = entry.Key;
                NationalData nationalData = entry.Value;

                if (nationalData == null || !nationalData.IsValid() || faction.IsPlayer) continue; // Only AI.

                // If current goal expired or no goal, choose a new one.
                if (nationalData.CurrentStrategicGoal == null || !nationalData.CurrentStrategicGoal.IsActive)
                {
                    AIStrategicGoalType newGoalType = AIStrategicGoalType.None;
                    NationalResourceType desiredResource = NationalResourceType.None;
                    Faction targetFaction = null;
                    int durationDays = Rand.Range(30, 90); // Goal lasts 30-90 days.

                    // Simple AI logic to pick a goal:
                    // 1. Check for critical resource shortages.
                    // 2. Assess military power relative to neighbors (for supremacy/expansion).
                    // 3. Consider current wealth for economic prosperity.

                    // Prioritize resource shortage.
                    if (nationalData.Resources.GetResourceAmount(NationalResourceType.Food) < 50)
                    {
                        newGoalType = AIStrategicGoalType.ResourceAcquisition;
                        desiredResource = NationalResourceType.Food;
                    }
                    else if (nationalData.Resources.GetResourceAmount(NationalResourceType.Steel) < 100)
                    {
                        newGoalType = AIStrategicGoalType.ResourceAcquisition;
                        desiredResource = NationalResourceType.Steel;
                    }
                    else if (nationalData.Resources.GetResourceAmount(NationalResourceType.Components) < 20)
                    {
                        newGoalType = AIStrategicGoalType.ResourceAcquisition;
                        desiredResource = NationalResourceType.Components;
                    }
                    else
                    {
                        // No critical shortage, choose based on policy and general situation.
                        float expansionScore = nationalData.CurrentPolicy.influenceSpreadModifier;
                        float militaryScore = nationalData.CurrentPolicy.militaryAggressionFactor;
                        float economicScore = nationalData.CurrentPolicy.tradeInterestFactor;

                        // Add situational modifiers.
                        if (nationalData.ControlledTilesCount < Find.WorldGrid.TilesCount / 20) expansionScore *= 1.5f; // Small factions want to grow.
                        if (nationalData.MilitaryPower < 100f) militaryScore *= 1.2f; // Weak factions want to build military.
                        if (nationalData.NationalWealth < 5000f) economicScore *= 1.3f; // Poor factions want wealth.

                        // If at war, prioritize military.
                        if (Find.FactionManager.AllFactionsListForReading.Any(f => f != faction && DiplomacyTracker.GetDiplomaticState(faction, f) == DiplomaticState.AtWar))
                        {
                            militaryScore *= 2.0f; // High priority for military during war.
                        }

                        // Pick the highest score, weighted randomly.
                        float totalScore = expansionScore + militaryScore + economicScore;
                        float randomRoll = Rand.Value * totalScore;

                        if (randomRoll < expansionScore)
                        {
                            newGoalType = AIStrategicGoalType.TerritorialExpansion;
                        }
                        else if (randomRoll < expansionScore + militaryScore)
                        {
                            newGoalType = AIStrategicGoalType.MilitarySupremacy;
                            // Optionally pick a rival as target.
                            targetFaction = Find.FactionManager.AllFactionsListForReading
                                .Where(f => f != faction && !f.IsPlayer && !f.Hidden && DiplomacyTracker.GetDiplomaticState(faction, f) == DiplomaticState.Rival)
                                .RandomElement();
                             if (targetFaction == null) targetFaction = Find.FactionManager.AllFactionsListForReading.Where(f => f != faction && !f.IsPlayer && !f.Hidden).RandomElement();
                        }
                        else
                        {
                            newGoalType = AIStrategicGoalType.EconomicProsperity;
                        }
                    }

                    nationalData.CurrentStrategicGoal = new StrategicGoal(newGoalType, durationDays, targetFaction, desiredResource);
                    Log.Message($"[NationalSystemMod.AI] {faction.Name} adopted a new strategic goal: {nationalData.CurrentStrategicGoal.GetGoalDescription()}");
                }
            }
        }

        // NEW: Method to create and spawn a new faction army.
        public static WorldArmy CreateFactionArmy(Faction faction, int originTile, float strength)
        {
            if (faction == null || !CoreLogic.NationalDataByFaction.ContainsKey(faction))
            {
                Log.Warning($"[NationalSystemMod] Tried to create an army for a faction with no NationalData: {faction?.Name ?? "NULL"}");
                return null;
            }

            WorldArmy army = (WorldArmy)WorldObjectMaker.MakeWorldObject(NationalSystemDefOf.NationalSystem_WorldArmy);
            army.Setup(faction, originTile, strength);
            
            Find.WorldObjects.Add(army);
            DiplomacyTracker?.AddArmy(army);

            Log.Message($"[NationalSystemMod] Created army for {faction.Name} at tile {originTile} with strength {strength:F0}.");
            return army;
        }

        // --- FUTURE ADDITIONS ---
        // Other event-driven methods or global utility functions can be added here as needed.
        // Examples: OnColonyAttacked, OnFactionDefeated, OnPawnDied.
    }
} 