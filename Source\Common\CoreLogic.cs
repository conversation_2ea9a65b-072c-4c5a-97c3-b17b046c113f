    using Verse;
    using RimWorld;
    using RimWorld.Planet;
    using UnityEngine;
    using System.Collections.Generic;
    using System.Linq;
    using AIGen.NationalSystemMod.World;
    using AIGen.NationalSystemMod.Nation;
    using AIGen.NationalSystemMod.Defs;
    using AIGen.NationalSystemMod.Economy;
    using AIGen.NationalSystemMod.StrategicAI;
    using AIGen.NationalSystemMod.Research;

    namespace AIGen.NationalSystemMod.Common
    {
        public static class CoreLogic
        {
            private static bool _isModSystemInitialized = false;
            public static bool IsModSystemInitialized => _isModSystemInitialized;

            public static WorldInfluenceMap WorldInfluenceMap { get; private set; }
            public static FactionDiplomacyTracker DiplomacyTracker { get; private set; }
            private static Dictionary<Faction, NationalData> _nationalDataByFaction;
            public static Dictionary<Faction, NationalData> NationalDataByFaction => _nationalDataByFaction;
            private static WorldEventManager _worldEventManager;
            public static WorldEventManager WorldEventManager => _worldEventManager;

            private const int RESOURCE_PRODUCTION_INTERVAL_TICKS = GenDate.TicksPerDay;
            private static int _lastResourceProductionTick = -1;

            private const int AI_GOAL_EVALUATION_INTERVAL_TICKS = GenDate.TicksPerDay * 3;
            private static int _lastAIGoalEvaluationTick = -1;
            
            private const int AI_POLICY_EVALUATION_INTERVAL_TICKS = GenDate.TicksPerDay * 10;
            private static int _lastAIPolicyEvaluationTick = -1;

            private const int RESEARCH_PROGRESS_INTERVAL_TICKS = 60;
            private static int _lastResearchProgressTick = -1;
            
            private const int WORLD_EVENT_INTERVAL_TICKS = GenDate.TicksPerDay;
            private static int _lastWorldEventTick = -1;
            
            public class CoreLogicGameComponent : GameComponent
            {
                private bool _hasInitializedOnce = false;
                
                public CoreLogicGameComponent(Game game) { }
                public CoreLogicGameComponent() { }

                public override void LoadedGame()
                {
                    base.LoadedGame();
                    InitializeModSystems();
                }

                public override void GameComponentTick()
                {
                    base.GameComponentTick();
                    if (!_hasInitializedOnce && Current.Game != null && Current.Game.tickManager.ticksGame > 200)
                    {
                        InitializeModSystems();
                        _hasInitializedOnce = true;
                    }
                    
                    if (IsModSystemInitialized)
                    {
                        PeriodicUpdate();
                    }
                }

                public override void ExposeData()
                {
                    base.ExposeData();
                    Scribe_Collections.Look(ref _nationalDataByFaction, "nationalDataByFaction", LookMode.Reference, LookMode.Deep);
                    Scribe_Deep.Look(ref _worldEventManager, "worldEventManager");
                    
                    if (Scribe.mode == LoadSaveMode.PostLoadInit)
                    {
                        if (_nationalDataByFaction == null) _nationalDataByFaction = new Dictionary<Faction, NationalData>();
                        if (_worldEventManager == null) _worldEventManager = new WorldEventManager();
                    }
                }
            }

            public static void InitializeModSystems()
            {
                if (_isModSystemInitialized) return;

                Log.Message("[NationalSystemMod.CoreLogic] Initializing Core Systems...");

                WorldInfluenceMap = Current.Game.GetComponent<WorldInfluenceMap>();
                if (WorldInfluenceMap == null)
                {
                    WorldInfluenceMap = new WorldInfluenceMap();
                    Current.Game.components.Add(WorldInfluenceMap);
                }

                DiplomacyTracker = Find.World.GetComponent<FactionDiplomacyTracker>();
                if (DiplomacyTracker == null)
                {
                    DiplomacyTracker = new FactionDiplomacyTracker(Find.World);
                    Find.World.components.Add(DiplomacyTracker);
                }

                var coreLogicComp = Current.Game.GetComponent<CoreLogicGameComponent>();
                if (coreLogicComp == null)
                {
                    coreLogicComp = new CoreLogicGameComponent();
                    Current.Game.components.Add(coreLogicComp);
                }
                
                if (_nationalDataByFaction == null) _nationalDataByFaction = new Dictionary<Faction, NationalData>();
                if (_worldEventManager == null) _worldEventManager = new WorldEventManager();

                EnsureNationalDataExistsForFactions();
                AssignPoliciesToFactions();
                CalculateInitialWorldInfluence();
                
                _worldEventManager.Init();

                _isModSystemInitialized = true;
                Log.Message("[NationalSystemMod.CoreLogic] Core Systems Initialized Successfully.");
            }

            private static void EnsureNationalDataExistsForFactions()
            {
                foreach (var faction in Find.FactionManager.AllFactionsVisible)
                {
                    if (faction.def.isPlayer || !faction.IsPlayer && faction.def.canBePlayerMechanicsFaction)
                    {
                        if (!_nationalDataByFaction.ContainsKey(faction))
                        {
                            _nationalDataByFaction[faction] = new NationalData(faction);
                            Log.Message($"[NationalSystemMod] Created NationalData for {faction.Name}");
                        }
                    }
                }
            }
            
            private static void AssignPoliciesToFactions()
            {
                foreach (var faction in Find.FactionManager.AllFactionsVisible)
                {
                    var nationalData = GetNationalData(faction);
                    if (nationalData == null) continue;

                    if (nationalData.UnlockedPolicies.Count == 0)
                    {
                        var defaultPolicies = DefDatabase<NationalPolicyDef>.AllDefs
                            .Where(p => p.isDefault)
                            .ToList();
                        
                        foreach (var policy in defaultPolicies)
                        {
                            nationalData.AddUnlockedPolicy(policy);
                            nationalData.AdoptPolicy(policy);
                            Log.Message($"[NationalSystemMod] Assigned and adopted default policy '{policy.defName}' to {faction.Name}");
                        }
                    }
                }
            }

            private static void CalculateInitialWorldInfluence()
            {
                Log.Message("[NationalSystemMod] Calculating initial world influence...");
                foreach (var faction in Find.FactionManager.AllFactionsVisible)
                {
                    if (faction.def.isPlayer || !faction.IsPlayer && faction.def.canBePlayerMechanicsFaction)
                    {
                        if (faction.HasHomeSettlement)
                        {
                            WorldInfluenceMap.UpdateInfluence(faction, faction.HomeSettlement, 25);
                        }
                    }
                }
                Log.Message("[NationalSystemMod] Initial world influence calculated.");
            }

            public static void PeriodicUpdate()
            {
                int currentTick = GenTicks.TicksGame;

                if (currentTick > _lastResourceProductionTick + RESOURCE_PRODUCTION_INTERVAL_TICKS)
                {
                    ProduceNationalResources();
                    _lastResourceProductionTick = currentTick;
                }

                if (currentTick > _lastAIGoalEvaluationTick + AI_GOAL_EVALUATION_INTERVAL_TICKS)
                {
                    EvaluateAIStrategicGoals();
                    _lastAIGoalEvaluationTick = currentTick;
                }
                
                if (currentTick > _lastAIPolicyEvaluationTick + AI_POLICY_EVALUATION_INTERVAL_TICKS)
                {
                    EvaluateAIPolicies();
                    _lastAIPolicyEvaluationTick = currentTick;
                }

                if (currentTick > _lastResearchProgressTick + RESEARCH_PROGRESS_INTERVAL_TICKS)
                {
                    AdvanceNationalResearch();
                    _lastResearchProgressTick = currentTick;
                }
                
                if (currentTick > _lastWorldEventTick + WORLD_EVENT_INTERVAL_TICKS)
                {
                    _worldEventManager?.WorldEventTick();
                    _lastWorldEventTick = currentTick;
                }

                // NEW: Handle army interactions (battles, etc.)
                HandleArmyInteractions();

                // Recalculate derived stats for all national data
                foreach (NationalData data in _nationalDataByFaction.Values)
                {
                    data.RecalculateDerivedStats();
                }
            }
            
            private static void AdvanceNationalResearch()
            {
                foreach (var factionData in _nationalDataByFaction)
                {
                    factionData.Value.ResearchManager?.Tick();
                }
            }

            private static void ProduceNationalResources()
            {
                foreach (var data in _nationalDataByFaction.Values)
                {
                    float foodProduction = 10f; 
                    float silverProduction = 5f;
                    float componentsProduction = 0.5f;

                    var adoptedPolicies = data.GetAdoptedPolicies();
                    foreach (var policy in adoptedPolicies)
                    {
                        foodProduction *= policy.foodProductionFactor;
                        silverProduction *= policy.silverProductionFactor;
                        componentsProduction *= policy.componentProductionFactor;
                    }
                    
                    data.UpdateResource(NationalResourceType.Food, foodProduction);
                    data.UpdateResource(NationalResourceType.Silver, silverProduction);
                    data.UpdateResource(NationalResourceType.Components, componentsProduction);
                }
            }

            private static void EvaluateAIStrategicGoals()
            {
                foreach (var faction in Find.FactionManager.AllFactionsListForReading)
                {
                    if (faction.IsPlayer || faction.def.hidden || !faction.def.canBePlayerMechanicsFaction) continue;

                    var nationalData = GetNationalData(faction);
                    if (nationalData == null) continue;

                    var goals = new Dictionary<StrategicGoalType, float>();

                    // Base weights
                    goals[StrategicGoalType.EconomicGrowth] = 1.0f;
                    goals[StrategicGoalType.MilitaryExpansion] = 0.5f;
                    goals[StrategicGoalType.TerritorialExpansion] = 0.5f;
                    goals[StrategicGoalType.TechnologicalAdvancement] = 0.8f;
                    goals[StrategicGoalType.DiplomaticSupremacy] = 0.6f;

                    // Adjust weights based on faction traits and situation
                    if (faction.def.permanentEnemy)
                    {
                        goals[StrategicGoalType.MilitaryExpansion] *= 2.0f;
                    }

                    if (nationalData.GetResource(NationalResourceType.Food) < 50)
                    {
                        goals[StrategicGoalType.EconomicGrowth] *= 1.5f;
                    }

                    var relations = DiplomacyTracker.GetAllRelationsForFaction(faction);
                    if (relations.Any(r => r.Value == DiplomaticState.AtWar))
                    {
                        goals[StrategicGoalType.MilitaryExpansion] *= 1.8f;
                    }
                    
                    if(nationalData.ResearchManager.CurrentResearch == null)
                    {
                         goals[StrategicGoalType.TechnologicalAdvancement] *= 2.0f;
                    }

                    var adoptedPolicies = nationalData.GetAdoptedPolicies();
                    foreach (var policy in adoptedPolicies)
                    {
                        goals[StrategicGoalType.EconomicGrowth] *= policy.aiEconomicWeight;
                        goals[StrategicGoalType.MilitaryExpansion] *= policy.aiMilitaryWeight;
                        goals[StrategicGoalType.TerritorialExpansion] *= policy.aiExpansionWeight;
                        goals[StrategicGoalType.TechnologicalAdvancement] *= policy.aiTechWeight;
                        goals[StrategicGoalType.DiplomaticSupremacy] *= policy.aiDiplomacyWeight;
                    }

                    StrategicGoalType chosenGoal = goals.OrderByDescending(kvp => kvp.Value * Rand.Range(0.8f, 1.2f)).First().Key;
                    nationalData.CurrentStrategicGoal = chosenGoal;

                    Log.Message($"[NationalSystemMod AI] Faction {faction.Name} has chosen strategic goal: {chosenGoal}");
                }
            }

            private static void EvaluateAIPolicies()
            {
                foreach (var faction in Find.FactionManager.AllFactionsListForReading)
                {
                    if (faction.IsPlayer || !faction.def.canBePlayerMechanicsFaction) continue;

                    var nationalData = GetNationalData(faction);
                    if (nationalData == null) continue;

                    var potentialPolicies = nationalData.UnlockedPolicies
                        .Where(p => !nationalData.IsPolicyAdopted(p.defName))
                        .ToList();

                    if (potentialPolicies.Count == 0) continue;

                    float bestPolicyScore = -1;
                    NationalPolicyDef bestPolicy = null;

                    foreach (var policy in potentialPolicies)
                    {
                        float score = EvaluatePolicySuitability(policy, nationalData);
                        if (score > bestPolicyScore)
                        {
                            bestPolicyScore = score;
                            bestPolicy = policy;
                        }
                    }

                    if (bestPolicy != null && bestPolicyScore > 0.5f)
                    {
                        nationalData.AdoptPolicy(bestPolicy);
                        Log.Message($"[NationalSystemMod AI] Faction {faction.Name} adopted new policy: {bestPolicy.label}");
                    }
                }
            }

            private static float EvaluatePolicySuitability(NationalPolicyDef policy, NationalData nationalData)
            {
                float score = 1.0f;
                var goal = nationalData.CurrentStrategicGoal;

                switch (goal)
                {
                    case StrategicGoalType.EconomicGrowth:
                        score *= policy.aiEconomicWeight;
                        break;
                    case StrategicGoalType.MilitaryExpansion:
                        score *= policy.aiMilitaryWeight;
                        break;
                    case StrategicGoalType.TerritorialExpansion:
                        score *= policy.aiExpansionWeight;
                        break;
                    case StrategicGoalType.TechnologicalAdvancement:
                        score *= policy.aiTechWeight;
                        break;
                    case StrategicGoalType.DiplomaticSupremacy:
                        score *= policy.aiDiplomacyWeight;
                        break;
                }

                if (nationalData.GetResource(NationalResourceType.Silver) < policy.adoptionCost)
                {
                    score *= 0.1f;
                }

                return score * Rand.Range(0.9f, 1.1f);
            }

            public static WorldArmy CreateFactionArmy(Faction faction, int originTile, float strength)
            {
                if (faction == null || originTile < 0) return null;

                var army = (WorldArmy)WorldObjectMaker.MakeWorldObject(DefDatabase<WorldObjectDef>.GetNamed("NationalArmy"));
                army.SetFaction(faction);
                army.Tile = originTile;
                army.Initialize(strength);
                Find.WorldObjects.Add(army);
                DiplomacyTracker.AddArmy(army);
                return army;
            }

            public static NationalData GetNationalData(Faction faction)
            {
                if (faction != null && _nationalDataByFaction.TryGetValue(faction, out var data))
                {
                    return data;
                }
                return null;
            }

            // NEW: Method to handle army interactions (very basic for now).
            private static void HandleArmyInteractions()
            {
                List<WorldArmy> allArmies = Find.WorldObjects.AllWorldObjects.OfType<WorldArmy>().ToList();

                // Group armies by tile.
                var armiesByTile = allArmies.GroupBy(army => army.Tile);

                foreach (var group in armiesByTile)
                {
                    if (group.Count() < 2) continue; // No interaction if only one army on tile.

                    List<WorldArmy> armiesOnTile = group.ToList();

                    // Simple collision detection for now: if hostile armies are on the same tile.
                    for (int i = 0; i < armiesOnTile.Count; i++)
                    {
                        for (int j = i + 1; j < armiesOnTile.Count; j++)
                        {
                            WorldArmy army1 = armiesOnTile[i];
                            WorldArmy army2 = armiesOnTile[j];

                            if (army1.Faction != army2.Faction && DiplomacyTracker.GetDiplomaticState(army1.Faction, army2.Faction) == DiplomaticState.AtWar)
                            {
                                // Hostile armies on same tile: Simulate a simple battle.
                                Log.Message($"[NationalSystemMod.Battle] {army1.Label} (F:{army1.Faction.Name}) and {army2.Label} (F:{army2.Faction.Name}) are clashing at tile {army1.Tile}!");

                                float battleStrength1 = army1.MilitaryStrength * Rand.Range(0.8f, 1.2f); // Add some randomness.
                                float battleStrength2 = army2.MilitaryStrength * Rand.Range(0.8f, 1.2f);

                                if (battleStrength1 > battleStrength2)
                                {
                                    // Army 1 wins
                                    army1.MilitaryStrength -= battleStrength2 * Rand.Range(0.3f, 0.7f); // Winner takes losses.
                                    army2.MilitaryStrength -= battleStrength1 * Rand.Range(0.8f, 1.2f); // Loser takes heavy losses.
                                    Messages.Message($"{army1.Label} defeated {army2.Label} at tile {army1.Tile}!", MessageTypeDefOf.PositiveEvent);
                                }
                                else if (battleStrength2 > battleStrength1)
                                {
                                    // Army 2 wins
                                    army2.MilitaryStrength -= battleStrength1 * Rand.Range(0.3f, 0.7f);
                                    army1.MilitaryStrength -= battleStrength2 * Rand.Range(0.8f, 1.2f);
                                    Messages.Message($"{army2.Label} defeated {army1.Label} at tile {army2.Tile}!", MessageTypeDefOf.NegativeEvent);
                                }
                                else
                                {
                                    // Draw / Mutual destruction (unlikely with randomness but possible)
                                    army1.MilitaryStrength -= battleStrength2 * 0.7f;
                                    army2.MilitaryStrength -= battleStrength1 * 0.7f;
                                    Messages.Message($"A fierce battle between {army1.Label} and {army2.Label} ended in a costly stalemate at tile {army1.Tile}!", MessageTypeDefOf.NeutralEvent);
                                }

                                // Clean up armies if their strength drops too low
                                if (army1.MilitaryStrength <= 0f)
                                {
                                    DiplomacyTracker.RemoveArmy(army1);
                                    army1.Destroy();
                                }
                                if (army2.MilitaryStrength <= 0f)
                                {
                                    DiplomacyTracker.RemoveArmy(army2);
                                    army2.Destroy();
                                }
                                
                                // Only one battle per tick per tile to avoid cascade effects
                                return;
                            }
                        }
                    }
                }
            }
        }
    }