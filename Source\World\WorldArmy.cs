using Verse;
using RimWorld;
using RimWorld.Planet;
using UnityEngine; // For Mathf
using System.Collections.Generic;
using System.Linq;
using AIGen.NationalSystemMod.Common;
using AIGen.NationalSystemMod.Nation;
using AIGen.NationalSystemMod.Economy;
using AIGen.NationalSystemMod.Utils; // For FactionExtensions
using AIGen.NationalSystemMod.Defs;
using AIGen.NationalSystemMod.Research; // NEW: For Research

namespace AIGen.NationalSystemMod.World
{
    // A WorldObject representing a faction's military force on the world map.
    public class WorldArmy : WorldObject
    {
        public new Faction Faction => base.Faction; // Inherited from WorldObject.
        public float MilitaryStrength; // Represents the army's combat power.
        public int OriginTile; // Where the army originated (e.g., a faction's core settlement).
        public int TargetTile; // Where the army is currently heading.
        public float DailyMovementRate = 0.5f; // Tiles per day.
        public List<Pawn> Pawns; // Future: to represent actual pawns, currently abstract.

        private const int SupplyConsumptionIntervalTicks = GenDate.TicksPerDay; // Daily consumption.
        private int _lastSupplyTick = -1;

        public override string Label => $"{Faction.Name} Army ({MilitaryStrength:F0})";
        public override string GetInspectString()
        {
            string str = base.GetInspectString();
            str += "\nStrength: " + MilitaryStrength.ToString("F0");
            str += "\nTarget: " + (TargetTile != -1 ? Find.WorldGrid[TargetTile].biome.label : "None");
            // Add remaining days to target.
            if (TargetTile != -1)
            {
                float travelDuration = Find.WorldGrid.ApproxDistanceInTiles(base.Tile, TargetTile) / DailyMovementRate;
                str += "\nArriving in: " + travelDuration.ToString("F1") + " days";
            }
            return str;
        }

        public WorldArmy()
        {
            def = NationalSystemDefOf.NationalSystem_WorldArmy; // Ensure you define this DefOf.
            TargetTile = -1;
            Pawns = new List<Pawn>(); // Initialize for future use.
        }

        public void Setup(Faction faction, int originTile, float strength, int initialTile = -1)
        {
            base.SetFaction(faction);
            this.OriginTile = originTile;
            this.MilitaryStrength = strength;
            if (initialTile == -1)
            {
                this.Tile = originTile;
            }
            else
            {
                this.Tile = initialTile;
            }
            //SpawnSetup(); // This is internal and should not be called directly.
        }

        public override void ExposeData()
        {
            base.ExposeData();
            Scribe_Values.Look(ref MilitaryStrength, "militaryStrength", 0f);
            Scribe_Values.Look(ref OriginTile, "originTile", 0);
            Scribe_Values.Look(ref TargetTile, "targetTile", -1);
            Scribe_Collections.Look(ref Pawns, "pawns", LookMode.Reference); // If you add actual pawns.
        }

        public override void Tick()
        {
            base.Tick();

            // Movement logic.
            if (TargetTile != -1 && Tile != TargetTile)
            {
                // Simple direct path for now.
                // For actual pathfinding, use RimWorld's caravan pathing.
                if (GenTicks.TicksGame % 60 == 0) // Every second.
                {
                    // Simple movement towards target (simplified pathfinding)
                    // Use a simple approach: move one tile closer to target
                    float currentDistance = Find.WorldGrid.ApproxDistanceInTiles(this.Tile, TargetTile);

                    // Try all adjacent tiles and pick the one closest to target
                    int bestTile = this.Tile;
                    float bestDistance = currentDistance;

                    // Check 6 directions (simplified hex grid movement)
                    for (int direction = 0; direction < 6; direction++)
                    {
                        int neighbor = Find.WorldGrid.GetTileNeighbor(this.Tile, direction);
                        if (neighbor >= 0 && neighbor < Find.WorldGrid.TilesCount)
                        {
                            float distance = Find.WorldGrid.ApproxDistanceInTiles(neighbor, TargetTile);
                            if (distance < bestDistance)
                            {
                                bestDistance = distance;
                                bestTile = neighbor;
                            }
                        }
                    }

                    if (bestTile != this.Tile)
                    {
                        this.Tile = bestTile;
                    }

                    if (this.Tile == TargetTile)
                    {
                        ArrivedAtTarget();
                    }
                }
            }

            // Supply consumption.
            if (GenTicks.TicksGame % SupplyConsumptionIntervalTicks == 0)
            {
                ConsumeSupplies();
            }

            // Check for interactions with other objects on the same tile.
            if (!this.Destroyed) // Don't check for interactions if destroyed in the same tick.
            {
                 CheckForInteractions();
            }
        }

        public void SetNewTarget(int tileID)
        {
            TargetTile = tileID;
            Log.Message($"[NationalSystemMod] {Label} setting new target to tile {tileID}.");
        }

        private void ArrivedAtTarget()
        {
            Log.Message($"[NationalSystemMod] {Label} arrived at target tile {Tile}.");
            TargetTile = -1; // Clear target after arrival.
            CheckForInteractions(); // Immediately check for interactions.
        }

        private void ConsumeSupplies()
        {
            NationalData nationalData = Faction.GetNationalData();
            if (nationalData == null) return;

            float foodConsumption = MilitaryStrength * 0.01f;
            float steelConsumption = MilitaryStrength * 0.001f;

            // NEW: Apply military upkeep reduction from research.
            float upkeepReductionFactor = 0f;
            foreach (NationalResearchDef completedResearch in nationalData.Research.CompletedResearch)
            {
                upkeepReductionFactor += completedResearch.militaryUpkeepReduction;
            }
            foodConsumption *= (1f - upkeepReductionFactor);
            steelConsumption *= (1f - upkeepReductionFactor);

            bool consumedFood = nationalData.Resources.TryConsumeResource(NationalResourceType.Food, foodConsumption);
            bool consumedSteel = nationalData.Resources.TryConsumeResource(NationalResourceType.Steel, steelConsumption);

            if (!consumedFood || !consumedSteel)
            {
                MilitaryStrength -= MilitaryStrength * 0.02f;
                MilitaryStrength = Mathf.Max(1f, MilitaryStrength);
                if (Faction.IsPlayer) Messages.Message($"{Label} is running low on supplies! Strength is decreasing.", MessageTypeDefOf.NegativeEvent);
            }
        }

        private void CheckForInteractions()
        {
             if (this.Destroyed) return;

            // Find other armies on this tile.
            List<WorldArmy> otherArmies = Find.WorldObjects.AllWorldObjects.OfType<WorldArmy>()
                .Where(army => army != this && !army.Destroyed && army.Tile == this.Tile && army.Faction != this.Faction)
                .ToList();

            foreach (WorldArmy otherArmy in otherArmies)
            {
                if (CoreLogic.DiplomacyTracker.GetDiplomaticState(this.Faction, otherArmy.Faction) == DiplomaticState.AtWar)
                {
                    EngageArmy(otherArmy);
                    return; // Only engage one army per tick.
                }
            }
            
            if (this.Destroyed) return; // Re-check as army could be destroyed in battle

            // Find settlements on this tile.
            Settlement settlement = Find.WorldObjects.Settlements.FirstOrDefault(s => s.Tile == this.Tile && s.Faction != this.Faction);
            if (settlement != null)
            {
                if (CoreLogic.DiplomacyTracker.GetDiplomaticState(this.Faction, settlement.Faction) == DiplomaticState.AtWar)
                {
                    SiegeSettlement(settlement);
                }
            }
        }

        private void EngageArmy(WorldArmy enemyArmy)
        {
            Log.Message($"[NationalSystemMod] {Label} engaging {enemyArmy.Label} at tile {Tile}!");
            // Basic combat resolution.
            float ourPower = MilitaryStrength * Rand.Range(0.8f, 1.2f); // Add some randomness.
            float enemyPower = enemyArmy.MilitaryStrength * Rand.Range(0.8f, 1.2f);

            NationalData ourData = Faction.GetNationalData();
            NationalData enemyData = enemyArmy.Faction.GetNationalData();

            // Policy influence on combat.
            if (ourData?.CurrentPolicy != null) ourPower *= ourData.CurrentPolicy.militaryAggressionFactor;
            if (enemyData?.CurrentPolicy != null) enemyPower *= enemyData.CurrentPolicy.militaryAggressionFactor;

            float totalPower = ourPower + enemyPower;
            if (totalPower <= 0) return; // Avoid division by zero
            float ourWinChance = ourPower / totalPower;

            if (Rand.Value < ourWinChance)
            {
                // We win!
                float strengthLoss = enemyArmy.MilitaryStrength * Rand.Range(0.3f, 0.7f); // Enemy loses significant strength.
                enemyArmy.MilitaryStrength -= strengthLoss;
                MilitaryStrength -= MilitaryStrength * Rand.Range(0.05f, 0.2f); // We take some losses too.

                if (enemyArmy.MilitaryStrength <= 1f)
                {
                    Messages.Message($"{Label} defeated {enemyArmy.Label}!", MessageTypeDefOf.PositiveEvent);
                    enemyArmy.Destroy();
                    // Award some resources/influence for victory.
                    if (ourData != null) ourData.NationalWealth += 100f;
                }
                else
                {
                    Messages.Message($"{Label} routed {enemyArmy.Label}!", MessageTypeDefOf.PositiveEvent);
                    if(enemyArmy.OriginTile > 0) enemyArmy.SetNewTarget(enemyArmy.OriginTile); // Retreat!
                }
            }
            else
            {
                // Enemy wins!
                float strengthLoss = MilitaryStrength * Rand.Range(0.3f, 0.7f);
                MilitaryStrength -= strengthLoss;
                enemyArmy.MilitaryStrength -= enemyArmy.MilitaryStrength * Rand.Range(0.05f, 0.2f);

                if (MilitaryStrength <= 1f)
                {
                    Messages.Message($"{enemyArmy.Label} defeated {Label}!", MessageTypeDefOf.NegativeEvent);
                    Destroy();
                    if (enemyData != null) enemyData.NationalWealth += 100f;
                }
                else
                {
                    Messages.Message($"{enemyArmy.Label} routed {Label}!", MessageTypeDefOf.NegativeEvent);
                    if(OriginTile > 0) SetNewTarget(OriginTile); // Retreat!
                }
            }
        }

        private void SiegeSettlement(Settlement settlement)
        {
            Log.Message($"[NationalSystemMod] {Label} besieging {settlement.Label} owned by {settlement.Faction.Name} at tile {Tile}!");

            NationalData attackingData = Faction.GetNationalData();
            NationalData defendingData = settlement.Faction.GetNationalData();
            
            if (attackingData == null || defendingData == null) return;

            float attackPower = MilitaryStrength * Rand.Range(0.8f, 1.2f);
            float defensePower = defendingData.MilitaryPower * Rand.Range(0.8f, 1.2f); // Settlement's defense.

            if (attackingData.CurrentPolicy != null) attackPower *= attackingData.CurrentPolicy.militaryAggressionFactor;
            if (defendingData.CurrentPolicy != null) defensePower *= defendingData.CurrentPolicy.militaryAggressionFactor;

            // Apply siege progress.
            float siegeProgress = attackPower - defensePower * 0.5f; // Harder to take a settlement.
            if (siegeProgress > 0)
            {
                // Reduce settlement's military power over time.
                defendingData.MilitaryPower -= siegeProgress * 0.1f;
                defendingData.MilitaryPower = Mathf.Max(0f, defendingData.MilitaryPower);

                Messages.Message($"{Label} is besieging {settlement.Label}. Defenders' strength reduced to {defendingData.MilitaryPower:F0}.", this, MessageTypeDefOf.NeutralEvent);

                // If settlement defense is broken, take it over.
                if (defendingData.MilitaryPower <= 10f) // Threshold for surrender.
                {
                    Messages.Message($"{Label} conquered {settlement.Label}!", MessageTypeDefOf.PositiveEvent);
                    // Change settlement ownership.
                    settlement.SetFaction(this.Faction);
                    CoreLogic.WorldInfluenceMap.SetInfluenceData(settlement.Tile, this.Faction, 1.0f); // Claim the tile fully

                    // Transfer some resources/wealth from defeated faction.
                    attackingData.NationalWealth += defendingData.NationalWealth * 0.1f;
                    defendingData.NationalWealth *= 0.8f; // Loser takes a hit.

                    // Send army home after conquest.
                    if (OriginTile > 0) SetNewTarget(OriginTile);
                    else Destroy(); // No home to return to, disband.
                }
            }
            else
            {
                // Siege fails/stalemate. Army takes losses.
                MilitaryStrength -= MilitaryStrength * Rand.Range(0.01f, 0.05f);
                MilitaryStrength = Mathf.Max(1f, MilitaryStrength);
                Messages.Message($"{Label} is struggling to besiege {settlement.Label}. Current strength: {MilitaryStrength:F0}.", this, MessageTypeDefOf.NegativeEvent);
            }
        }
    }
} 