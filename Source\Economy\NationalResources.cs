using Verse;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace AIGen.NationalSystemMod.Economy
{
    // Enum for different resource types in the national system.
    public enum NationalResourceType
    {
        None,
        Food,
        Steel,
        Components,
        AdvancedComponents,
        Silver // Represents general wealth, distinct from NationalData.NationalWealth
    }

    // Represents a faction's resource stockpiles.
    public class NationalResources : IExposable
    {
        private Dictionary<NationalResourceType, float> _resources;

        public NationalResources()
        {
            _resources = new Dictionary<NationalResourceType, float>();
            // Initialize with some base amounts.
            foreach (NationalResourceType type in System.Enum.GetValues(typeof(NationalResourceType)))
            {
                if (type == NationalResourceType.None) continue;
                _resources[type] = 0f; // Start empty or with a small base.
            }
            _resources[NationalResourceType.Food] = 500f;
            _resources[NationalResourceType.Steel] = 200f;
            _resources[NationalResourceType.Components] = 50f;
            _resources[NationalResourceType.Silver] = 1000f;
        }

        public float GetResourceAmount(NationalResourceType type)
        {
            if (_resources.TryGetValue(type, out float amount))
            {
                return amount;
            }
            return 0f;
        }

        public void AddResource(NationalResourceType type, float amount)
        {
            if (amount < 0)
            {
                Log.Warning($"Attempted to add negative resource amount for {type}: {amount}");
                return;
            }
            if (_resources.ContainsKey(type))
            {
                _resources[type] += amount;
            }
            else
            {
                _resources[type] = amount;
            }
            // Cap resources to prevent absurd numbers, if desired.
            _resources[type] = Mathf.Min(_resources[type], 100000f);
        }

        public bool TryConsumeResource(NationalResourceType type, float amount)
        {
            if (amount < 0)
            {
                Log.Warning($"Attempted to consume negative resource amount for {type}: {amount}");
                return false;
            }
            if (_resources.TryGetValue(type, out float currentAmount) && currentAmount >= amount)
            {
                _resources[type] -= amount;
                return true;
            }
            return false;
        }

        // Alias for AddResource for compatibility
        public void UpdateResource(NationalResourceType type, float amount)
        {
            AddResource(type, amount);
        }

        public void ExposeData()
        {
            Scribe_Collections.Look(ref _resources, "resources", LookMode.Value, LookMode.Value);
            // Handle null dictionary after load for new games.
            if (Scribe.mode == LoadSaveMode.PostLoadInit && _resources == null)
            {
                _resources = new Dictionary<NationalResourceType, float>();
                // Re-initialize if loaded into a game where this was not saved.
                foreach (NationalResourceType type in System.Enum.GetValues(typeof(NationalResourceType)))
                {
                    if (type == NationalResourceType.None) continue;
                    _resources[type] = 0f; 
                }
            }
        }
    }
}
