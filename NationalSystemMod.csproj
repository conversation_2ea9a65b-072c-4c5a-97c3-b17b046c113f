<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net472</TargetFramework>
    <LangVersion>latest</LangVersion>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <OutputPath>Assemblies\</OutputPath>

    <AssemblyName>NationalSystemMod</AssemblyName>
    <RootNamespace>AIGen.NationalSystemMod</RootNamespace>

    <!-- This is the path to your RimWorld installation. -->
    <RimWorldPath>D:\Program Files (x86)\Steam\steamapps\common\RimWorld</RimWorldPath>
    <!-- Alternative common paths for RimWorld -->
    <RimWorldPath Condition="!Exists('$(RimWorldPath)')">C:\Program Files (x86)\Steam\steamapps\common\RimWorld</RimWorldPath>
    <RimWorldPath Condition="!Exists('$(RimWorldPath)')">$(USERPROFILE)\AppData\Local\Programs\RimWorld</RimWorldPath>
  </PropertyGroup>

  <ItemGroup>
    <Reference Include="Assembly-CSharp">
      <HintPath>$(RimWorldPath)\RimWorldWin64_Data\Managed\Assembly-CSharp.dll</HintPath>
      <Private>false</Private>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>$(RimWorldPath)\RimWorldWin64_Data\Managed\UnityEngine.IMGUIModule.dll</HintPath>
      <Private>false</Private>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>$(RimWorldPath)\RimWorldWin64_Data\Managed\UnityEngine.TextRenderingModule.dll</HintPath>
      <Private>false</Private>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Krafs.Publicizer" Version="2.2.1">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Lib.Harmony" Version="2.2.2" />
  </ItemGroup>

  <ItemGroup>
    <Publicize Include="Assembly-CSharp" />
  </ItemGroup>

</Project>