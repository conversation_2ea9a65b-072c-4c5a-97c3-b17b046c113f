---
description: 
globs: 
alwaysApply: true
---
RimWorld_NationalSystem_Mod/
├── About/
│   └── About.xml             // Mod information (name, version, description, compatibility, etc.)
├── Assemblies/
│   └── Mod.dll               // Compiled mod file (final output from AI compilation)
├── Common/                   // Globally used code or utilities
│   └── CoreLogic.cs          // Core mod logic (likely where most AI-generated code will reside)
├── Defs/                     // XML definition files (RimWorld data definitions)
│   ├── FactionDefs/          // National faction definitions (new nation types, existing faction modifications)
│   │   └── NationalFactionDefs.xml
│   ├── ThingDefs_Buildings/  // National system-related building definitions (border outposts, capital buildings, etc.)
│   │   └── NationalBuildings.xml
│   ├── HediffDefs/           // Hediff definitions (related to national instability, etc.)
│   ├── IncidentDefs/         // Event definitions (nation declaration events, diplomatic events, rebellion events, etc.)
│   │   └── NationalIncidents.xml
│   ├── ResearchProjectDefs/  // Nation-related research project definitions (diplomatic tech, administrative tech, etc.)
│   │   └── NationalResearch.xml
│   └── WorldObjectDefs/      // World map objects related to nations (armies, special strongholds, etc.)
│       └── NationalWorldObjects.xml
├── Languages/                // Translation files
│   └── English/
│       └── Keyed/
│           └── NationalSystem_Keys.xml
│       └── Strings/
│           └── NationalSystem_Strings.xml
├── Patches/                  // Files for patching existing RimWorld data (XML)
│   └── FactionPatch.xml      // Add national system properties to existing factions, etc.
│   └── WorldMapPatch.xml     // Patch world map-related data
├── Textures/                 // Image files used by the mod
│   ├── UI/                   // UI icons, backgrounds, etc.
│   └── World/                // Textures for world map display (border line textures, etc.)
├── Source/                   // C# source code (primarily to be generated by AI)
│   ├── NationalSystemMod.cs  // Mod entry point and initialization
│   ├── Managers/             // Various system managers (DiplomacyManager, EconomyManager, MilitaryManager)
│   │   ├── DiplomacyManager.cs
│   │   ├── EconomyManager.cs
│   │   └── MilitaryManager.cs
│   ├── Components/           // Components attached to world objects or pawns
│   │   └── NationalInfluenceComp.cs // Influence component for cities or nations
│   ├── UI/                   // UI-related code (nation info window, diplomacy window, etc.)
│   │   ├── NationalInfoWindow.cs
│   │   └── DiplomacyWindow.cs
│   ├── World/                // World map-related logic (drawing borders, world map movement, etc.)
│   │   └── NationalBorderDrawer.cs
│   │   └── WorldInfluenceMap.cs
│   └── Utilities/            // Common utility functions
│       └── OptimizationUtils.cs // Optimization utilities (periodic update scheduler, etc.)

Note:

The .cs files in the Source/ folder are the C# code files that Cursor AI will generate.
The .xml files in the Defs/ folder define mod elements according to RimWorld's data structure. It would be beneficial if Cursor AI could understand and populate these XML files.


Mod.dll is the file generated when the mod is finally built