<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <WorldObjectDef>
    <defName>NationalSystem_WorldArmy</defName>
    <label>army</label>
    <description>A mobile military force representing a faction's strength on the world map.</description>
    <worldObjectClass>AIGen.NationalSystemMod.World.WorldArmy</worldObjectClass>
    <texture>World/WorldObjects/TravelingRelic</texture>
    <useDynamicTexture>true</useDynamicTexture>
    <category>Caravan</category>
    <expandingIcon>true</expandingIcon>
    <expandingIconTexture>World/WorldObjects/Expanding/Camp</expandingIconTexture>
    <expandingIconPriority>70</expandingIconPriority>
    <allowNestedScrolling>false</allowNestedScrolling>
    <inspectorTabs>
      <li>ITab_WorldObject_TransporterContents</li>
    </inspectorTabs>
  </WorldObjectDef>

</Defs> 