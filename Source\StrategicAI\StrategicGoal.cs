using Verse;
using RimWorld;
using System.Collections.Generic;
using AIGen.NationalSystemMod.Economy;

namespace AIGen.NationalSystemMod.StrategicAI // New namespace for AI.
{
    // Enum for different strategic goals an AI faction can have.
    public enum AIStrategicGoalType
    {
        None,
        TerritorialExpansion,  // Focus on gaining more tiles.
        ResourceAcquisition,   // Focus on tiles with specific resources.
        MilitarySupremacy,     // Focus on increasing military power, perhaps through conquest.
        EconomicProsperity     // Focus on wealth and trade.
    }

    // Defines a strategic goal and its parameters.
    public class StrategicGoal : IExposable
    {
        public AIStrategicGoalType GoalType;
        public int StartTick;
        public int DurationTicks; // How long this goal is pursued.
        public Faction TargetFaction; // For MilitarySupremacy against a specific foe.
        public NationalResourceType DesiredResource; // For ResourceAcquisition.
        public World.WorldArmy AssignedArmy; // NEW: The army assigned to this task.

        public bool IsActive => GenTicks.TicksGame < StartTick + DurationTicks;

        public StrategicGoal() { } // Parameterless constructor for Scribe.

        public StrategicGoal(AIStrategicGoalType type, int durationDays, Faction target = null, NationalResourceType resource = NationalResourceType.None)
        {
            GoalType = type;
            StartTick = GenTicks.TicksGame;
            DurationTicks = durationDays * GenDate.TicksPerDay;
            TargetFaction = target;
            DesiredResource = resource;
            AssignedArmy = null; // Initially no army is assigned.
        }

        public void ExposeData()
        {
            Scribe_Values.Look(ref GoalType, "goalType");
            Scribe_Values.Look(ref StartTick, "startTick");
            Scribe_Values.Look(ref DurationTicks, "durationTicks");
            Scribe_References.Look(ref TargetFaction, "targetFaction");
            Scribe_Values.Look(ref DesiredResource, "desiredResource");
            Scribe_References.Look(ref AssignedArmy, "assignedArmy"); // NEW: Scribe the army reference.
        }

        public string GetGoalDescription()
        {
            switch (GoalType)
            {
                case AIStrategicGoalType.TerritorialExpansion: return "Territorial Expansion";
                case AIStrategicGoalType.ResourceAcquisition: return $"Resource Acquisition ({DesiredResource.ToString()})";
                case AIStrategicGoalType.MilitarySupremacy: return TargetFaction != null ? $"Military Supremacy (vs. {TargetFaction.Name})" : "Military Supremacy (General)";
                case AIStrategicGoalType.EconomicProsperity: return "Economic Prosperity";
                default: return "No Specific Goal";
            }
        }
    }
}