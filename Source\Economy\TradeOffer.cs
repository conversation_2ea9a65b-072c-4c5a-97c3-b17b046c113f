using Verse;
using RimWorld;
using AIGen.NationalSystemMod.Nation; // For NationalData
using AIGen.NationalSystemMod.Utils; // For FactionExtensions
using System.Collections.Generic;
using System.Linq;

namespace AIGen.NationalSystemMod.Economy // New namespace for economy-related classes.
{
    // Represents a potential trade offer between two factions. Not persistent.
    // The actual trade is a one-time event that consumes resources and provides silver/other resources.
    public class TradeOffer : IExposable
    {
        public Faction ProposingFaction;
        public Faction TargetFaction;
        public Dictionary<NationalResourceType, float> ResourcesOffered; // Resources player offers.
        public Dictionary<NationalResourceType, float> ResourcesRequested; // Resources player wants.
        public int ExpiryTick;

        // Base value of the offer, for UI and AI evaluation.
        public float OfferValue => CalculateOfferValue(ResourcesOffered) - CalculateOfferValue(ResourcesRequested);

        public TradeOffer() 
        {
            ResourcesOffered = new Dictionary<NationalResourceType, float>();
            ResourcesRequested = new Dictionary<NationalResourceType, float>();
        }

        public TradeOffer(Faction proposer, Faction target, Dictionary<NationalResourceType, float> offered, Dictionary<NationalResourceType, float> requested, int durationDays)
        {
            ProposingFaction = proposer;
            TargetFaction = target;
            ResourcesOffered = offered ?? new Dictionary<NationalResourceType, float>();
            ResourcesRequested = requested ?? new Dictionary<NationalResourceType, float>();
            ExpiryTick = GenTicks.TicksGame + (durationDays * GenDate.TicksPerDay);
        }

        public void ExposeData()
        {
            Scribe_References.Look(ref ProposingFaction, "proposingFaction");
            Scribe_References.Look(ref TargetFaction, "targetFaction");
            Scribe_Collections.Look(ref ResourcesOffered, "resourcesOffered", LookMode.Value, LookMode.Value);
            Scribe_Collections.Look(ref ResourcesRequested, "resourcesRequested", LookMode.Value, LookMode.Value);
            Scribe_Values.Look(ref ExpiryTick, "expiryTick");

            if (Scribe.mode == LoadSaveMode.PostLoadInit)
            {
                if (ResourcesOffered == null) ResourcesOffered = new Dictionary<NationalResourceType, float>();
                if (ResourcesRequested == null) ResourcesRequested = new Dictionary<NationalResourceType, float>();
            }
        }

        // Helper to calculate value (this should be based on a central resource value table).
        private float CalculateOfferValue(Dictionary<NationalResourceType, float> resources)
        {
            float value = 0f;
            if (resources == null) return value;
            foreach (var entry in resources)
            {
                value += entry.Value * GetResourceMarketValue(entry.Key);
            }
            return value;
        }

        // Placeholder for resource market values. Make this more sophisticated later.
        public static float GetResourceMarketValue(NationalResourceType type)
        {
            switch (type)
            {
                case NationalResourceType.Food: return 0.5f;
                case NationalResourceType.Steel: return 1.0f;
                case NationalResourceType.Components: return 4.0f;
                case NationalResourceType.AdvancedComponents: return 15.0f;
                case NationalResourceType.Silver: return 1.0f;
                default: return 0f;
            }
        }

        // Execute the trade (consumes and adds resources).
        public bool TryExecuteTrade()
        {
            if (ProposingFaction == null || TargetFaction == null) return false;
            NationalData proposerData = ProposingFaction.GetNationalData();
            NationalData targetData = TargetFaction.GetNationalData();
            if (proposerData == null || targetData == null) return false;

            // Check if proposer has enough resources to offer.
            foreach (var entry in ResourcesOffered)
            {
                if (proposerData.Resources.GetResourceAmount(entry.Key) < entry.Value)
                {
                    if (ProposingFaction.IsPlayer) Messages.Message($"You don't have enough {entry.Key} to make this trade.", MessageTypeDefOf.RejectInput, false);
                    return false;
                }
            }
            // Check if target has enough resources to fulfill request.
            foreach (var entry in ResourcesRequested)
            {
                if (targetData.Resources.GetResourceAmount(entry.Key) < entry.Value)
                {
                    if (ProposingFaction.IsPlayer) Messages.Message($"They don't have enough {entry.Key} to fulfill your request.", MessageTypeDefOf.RejectInput, false);
                    return false;
                }
            }

            // Perform transaction.
            foreach (var entry in ResourcesOffered)
            {
                proposerData.Resources.TryConsumeResource(entry.Key, entry.Value);
                targetData.Resources.AddResource(entry.Key, entry.Value);
            }
            foreach (var entry in ResourcesRequested)
            {
                targetData.Resources.TryConsumeResource(entry.Key, entry.Value);
                proposerData.Resources.AddResource(entry.Key, entry.Value);
            }

            // Improve goodwill slightly.
            ProposingFaction.TryAffectGoodwillWith(TargetFaction, 5, canSendMessage: false, canSendHostilityLetter: false, reason: HistoryEventDefOf.Traded);
            Messages.Message($"Trade executed between {ProposingFaction.Name} and {TargetFaction.Name}!", MessageTypeDefOf.PositiveEvent);
            return true;
        }
    }
} 