using Verse;
using RimWorld;
using RimWorld.Planet;
using System.Collections.Generic;
using NationalSystemMod.Economy;
using NationalSystemMod.Nation;
using NationalSystemMod.Common; // For CoreLogic
using NationalSystemMod.Diplomacy;
using UnityEngine;

namespace NationalSystemMod.World
{
    public class ActiveWorldEvent : IExposable
    {
        public WorldEventDef def;
        public int startTick;
        public int endTick;

        // Affected entities.
        public int affectedTile = -1; // For affectsSingleTile events.
        public Faction affectedFaction = null; // For affectsFaction events.

        public bool IsActive => GenTicks.TicksGame < endTick;

        public ActiveWorldEvent() { }

        public ActiveWorldEvent(WorldEventDef def, int durationTicks, int tile = -1, Faction faction = null)
        {
            this.def = def;
            this.startTick = GenTicks.TicksGame;
            this.endTick = GenTicks.TicksGame + durationTicks;
            this.affectedTile = tile;
            this.affectedFaction = faction;
            
            // Apply one-time effects immediately upon creation.
            ApplyOneTimeEffects();
            DiplomacyTracker.ApplyEventGoodwillChanges(def, faction);
            SendEventNotification();
        }

        private void ApplyOneTimeEffects()
        {
            if (def.militaryPowerGain > 0 || def.militaryPowerLoss > 0)
            {
                if (def.affectsFaction && affectedFaction != null)
                {
                    NationalData nationalData = affectedFaction.GetNationalData();
                    if (nationalData != null)
                    {
                        nationalData.MilitaryPower += def.militaryPowerGain;
                        nationalData.MilitaryPower -= def.militaryPowerLoss;
                        nationalData.MilitaryPower = Mathf.Max(0, nationalData.MilitaryPower);
                    }
                }
                else if (def.affectsAllFactions)
                {
                    foreach (var entry in CoreLogic.NationalDataByFaction)
                    {
                        NationalData nationalData = entry.Value;
                        if (nationalData != null && nationalData.IsValid())
                        {
                            nationalData.MilitaryPower += def.militaryPowerGain;
                            nationalData.MilitaryPower -= def.militaryPowerLoss;
                            nationalData.MilitaryPower = Mathf.Max(0, nationalData.MilitaryPower);
                        }
                    }
                }
            }
        }

        private void SendEventNotification()
        {
            string message = $"{def.label}: {def.description}";
            MessageTypeDef type = MessageTypeDefOf.NeutralEvent;

            if (def.affectsFaction && affectedFaction != null)
            {
                if (affectedFaction.IsPlayer) type = MessageTypeDefOf.PositiveEvent; // Player-centric notification.
                message = $"{affectedFaction.Name} is affected by {def.label}: {def.description}";
                Messages.Message(message, type);
            }
            else if (def.affectsSingleTile && affectedTile != -1)
            {
                message = $"An event, {def.label}, occurred at tile {affectedTile}: {def.description}";
                Messages.Message(message, type);
            }
            else if (def.affectsAllFactions)
            {
                Messages.Message(message, type);
            }
        }

        // Call this periodically to apply ongoing effects.
        public void EventTick()
        {
            if (!IsActive) return;

            // Apply ongoing resource effects.
            if (def.ResourceGainPerDay != null || def.ResourceLossPerDay != null)
            {
                if (def.affectsFaction && affectedFaction != null)
                {
                    ApplyResourceEffectsToFaction(affectedFaction.GetNationalData());
                }
                else if (def.affectsAllFactions)
                {
                    foreach (var entry in CoreLogic.NationalDataByFaction)
                    {
                        ApplyResourceEffectsToFaction(entry.Value);
                    }
                }
                else if (def.affectsSingleTile && affectedTile != -1)
                {
                    Faction ownerFaction = CoreLogic.WorldInfluenceMap.GetInfluenceData(affectedTile)?.faction;
                    if (ownerFaction != null)
                    {
                        ApplyResourceEffectsToFaction(ownerFaction.GetNationalData());
                    }
                }
            }

            // Apply ongoing influence effects (if designed to be ongoing).
            if (def.influenceGainPerDay != 0f || def.influenceLossPerDay != 0f)
            {
                 if (def.affectsFaction && affectedFaction != null)
                {
                    NationalData nationalData = affectedFaction.GetNationalData();
                    if (nationalData != null)
                    {
                        nationalData.TotalInfluenceScore += def.influenceGainPerDay / GenDate.TicksPerDay;
                        nationalData.TotalInfluenceScore -= def.influenceLossPerDay / GenDate.TicksPerDay;
                    }
                }
                // Global/tile-based influence changes would need more complex logic.
            }

            // Goodwill change only at start for now, or could be ongoing.
        }

        private void ApplyResourceEffectsToFaction(NationalData nationalData)
        {
            if (nationalData == null) return;

            if (def.ResourceGainPerDay != null)
            {
                foreach (var resEntry in def.ResourceGainPerDay)
                {
                    nationalData.Resources.AddResource(resEntry.Key, resEntry.Value / GenDate.TicksPerDay);
                }
            }
            if (def.ResourceLossPerDay != null)
            {
                foreach (var resEntry in def.ResourceLossPerDay)
                {
                    nationalData.Resources.TryConsumeResource(resEntry.Key, resEntry.Value / GenDate.TicksPerDay);
                }
            }
        }


        public void ExposeData()
        {
            Scribe_Defs.Look(ref def, "def");
            Scribe_Values.Look(ref startTick, "startTick");
            Scribe_Values.Look(ref endTick, "endTick");
            Scribe_Values.Look(ref affectedTile, "affectedTile", -1);
            Scribe_References.Look(ref affectedFaction, "affectedFaction");
        }
    }
} 