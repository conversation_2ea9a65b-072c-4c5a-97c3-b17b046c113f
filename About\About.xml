<?xml version="1.0" encoding="utf-8"?>
<ModMetaData>
    <name>National System Mod</name>
    <author>AIGen</author>
    <supportedVersions>
        <li>1.6</li>
    </supportedVersions>
    <packageId>AIGen.NationalSystemMod</packageId>
    <description>
        Introducing a deep national system to Rim<PERSON>orld, allowing players to evolve colonies into nations, engage in diplomacy, warfare, and economic management for world conquest or resource acquisition through colonies/vassals. This mod integrates and enriches the existing faction system.
        
        A core focus of this mod is **performance optimization**, specifically minimizing tick generation to ensure a smooth gameplay experience.
    </description>
    <modDependencies>
        <li>
            <packageId>Ludeon.RimWorld.Royalty</packageId>
            <displayName>Royalty</displayName>
        </li>
        <li>
            <packageId>Ludeon.RimWorld.Ideology</packageId>
            <displayName>Ideology</displayName>
        </li>
    </modDependencies>
    <loadAfter>
        <li>Ludeon.RimWorld</li>
        <li>Ludeon.RimWorld.Royalty</li>
        <li>Ludeon.RimWorld.Ideology</li>
    </loadAfter>
</ModMetaData> 