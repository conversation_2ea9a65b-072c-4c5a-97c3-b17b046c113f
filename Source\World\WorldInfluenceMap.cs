using Verse; // Basic RimWorld utilities.
using RimWorld; // For Faction
using RimWorld.Planet; // For World-specific classes like WorldGrid.
using System.Collections.Generic; // For collections.
using System.Linq;
using AIGen.NationalSystemMod.Economy; // NEW: For NationalResourceType.

namespace AIGen.NationalSystemMod.World // IMPORTANT: Use your actual package ID and include .World.
{
    // This class will manage influence data for each tile on the world map.
    // It's designed to be a component or a globally accessible manager.
    public class WorldInfluenceMap : GameComponent, IExposable // Implement IExposable for saving/loading influence data.
    {
        // Stores the influence data for each world tile. Key: tile ID (int), Value: InfluenceTileData.
        // Using a dictionary for sparse data (not all tiles might be actively influenced).
        // For performance, consider using an array if influence data is dense across many tiles.
        // For now, dictionary is safer for sparse data.
        private Dictionary<int, InfluenceTileData> _influenceDataByTile;

        // Public property to access the influence data (read-only from outside).
        public Dictionary<int, InfluenceTileData> InfluenceDataByTile
        {
            get
            {
                if (_influenceDataByTile == null)
                {
                    _influenceDataByTile = new Dictionary<int, InfluenceTileData>();
                }
                return _influenceDataByTile;
            }
        }

        // GameComponent constructor
        public WorldInfluenceMap()
        {
            _influenceDataByTile = new Dictionary<int, InfluenceTileData>();
        }

        // Alternative constructor for RimWorld's reflection
        public WorldInfluenceMap(Game game)
        {
            _influenceDataByTile = new Dictionary<int, InfluenceTileData>();
        }

        // Method to get influence data for a specific tile.
        // Returns null if the tile has no explicit influence data (i.e., it's neutral).
        public InfluenceTileData GetInfluenceData(int tileID)
        {
            // Ensure _influenceDataByTile is initialized
            if (_influenceDataByTile == null)
            {
                _influenceDataByTile = new Dictionary<int, InfluenceTileData>();
                return null;
            }

            if (_influenceDataByTile.TryGetValue(tileID, out InfluenceTileData data))
            {
                return data;
            }
            return null; // No specific influence data for this tile.
        }

        // Method to set or update influence data for a specific tile.
        // This is a key method for our dynamic border system.
        public void SetInfluenceData(int tileID, Faction faction, float influenceAmount)
        {
            // Ensure _influenceDataByTile is initialized
            if (_influenceDataByTile == null)
            {
                _influenceDataByTile = new Dictionary<int, InfluenceTileData>();
            }

            // IMPORTANT: Ensure faction is not null and influenceAmount is valid.
            if (faction == null)
            {
                // If influenceAmount is 0 or less, consider removing the entry for neutrality
                if (influenceAmount <= 0 && _influenceDataByTile.ContainsKey(tileID))
                {
                    _influenceDataByTile.Remove(tileID);
                    return;
                }
                Log.Warning($"[NationalSystemMod.WorldInfluenceMap] Attempted to set influence for tile {tileID} with null faction.");
                return;
            }

            if (_influenceDataByTile.TryGetValue(tileID, out InfluenceTileData data))
            {
                // Update existing data
                data.faction = faction;
                data.influenceAmount = influenceAmount;
            }
            else
            {
                // Add new data
                _influenceDataByTile.Add(tileID, new InfluenceTileData(faction, influenceAmount));
            }
        }

        // Method to remove influence data for a tile (e.g., if it becomes completely neutral).
        public void RemoveInfluenceData(int tileID)
        {
            if (_influenceDataByTile != null)
            {
                _influenceDataByTile.Remove(tileID);
            }
        }

        // IExposable implementation for saving and loading. Crucial for mod data persistence.
        public override void ExposeData()
        {
            // Scribe_Collections.Look handles saving/loading dictionaries safely.
            Scribe_Collections.Look(ref _influenceDataByTile, "influenceDataByTile", LookMode.Value, LookMode.Deep);

            // If loading from an old save where _influenceDataByTile was null, ensure it's re-initialized.
            if (_influenceDataByTile == null)
            {
                _influenceDataByTile = new Dictionary<int, InfluenceTileData>();
            }
        }
    }

    // --- New Class: InfluenceTileData ---
    // This nested class defines the data stored for each influenced tile.
    public class InfluenceTileData : IExposable
    {
        public Faction faction; // The faction that influences this tile.
        public float influenceAmount; // The amount of influence (e.g., 0.0 to 1.0 or a raw score).
        public int lastInfluenceTick;

        // NEW: Resource output potential for this tile.
        public Dictionary<NationalResourceType, float> ResourceOutput; 

        // Default constructor for IExposable (required).
        public InfluenceTileData() 
        {
            // Initialize resource output for new tiles.
            ResourceOutput = new Dictionary<NationalResourceType, float>();
            InitializeDefaultResourceOutput();
        }

        public InfluenceTileData(Faction faction, float influenceAmount) : this() // Call default constructor.
        {
            this.faction = faction;
            this.influenceAmount = influenceAmount;
            this.lastInfluenceTick = GenTicks.TicksGame;
        }

        private void InitializeDefaultResourceOutput()
        {
            // Assign some random resource output to tiles. This will be more nuanced later.
            // For now, a simple random assignment.
            float baseAmount = 5f;
            float variability = 0.5f;

            foreach (NationalResourceType type in System.Enum.GetValues(typeof(NationalResourceType)))
            {
                if (type == NationalResourceType.None || type == NationalResourceType.Silver) continue; // Silver is usually generated from economy, not tiles.

                // Make some resources rarer.
                float currentBase = baseAmount;
                if (type == NationalResourceType.Components) currentBase = 1f;
                if (type == NationalResourceType.AdvancedComponents) currentBase = 0.1f;

                ResourceOutput[type] = currentBase + (Rand.Value * currentBase * variability);
            }
        }

        // IExposable implementation for saving and loading this data.
        public void ExposeData()
        {
            Scribe_References.Look(ref faction, "faction"); // Save reference to the Faction.
            Scribe_Values.Look(ref influenceAmount, "influenceAmount", 0f);
            Scribe_Values.Look(ref lastInfluenceTick, "lastInfluenceTick", 0);
            
            // NEW: Scribe ResourceOutput.
            Scribe_Collections.Look(ref ResourceOutput, "resourceOutput", LookMode.Value, LookMode.Value);
            // Ensure initialized on load if it's a new tile or old save.
            if (Scribe.mode == LoadSaveMode.PostLoadInit && ResourceOutput == null)
            {
                ResourceOutput = new Dictionary<NationalResourceType, float>();
                InitializeDefaultResourceOutput();
            }
        }
    }
} 