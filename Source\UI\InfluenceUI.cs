using Verse;
using Rim<PERSON>orld; // For various UI elements like Listing_Standard, Widgets.
using RimWorld.Planet; // For Settlement
using UnityEngine; // For Rect, GUI.
using System.Linq; // For LINQ operations if needed.
using AIGen.NationalSystemMod.Common; // For CoreLogic access.
using AIGen.NationalSystemMod.Nation; // For NationalData access.
using AIGen.NationalSystemMod.World; // NEW: For DiplomaticState enum.
using AIGen.NationalSystemMod.Utils;
using AIGen.NationalSystemMod.Economy; // NEW: For TradeAgreement and NationalResourceType
using AIGen.NationalSystemMod.StrategicAI; // NEW: For StrategicGoal
using AIGen.NationalSystemMod.Research; // NEW: For Research
using System.Collections.Generic;

namespace AIGen.NationalSystemMod.UI // New namespace for UI.
{
    // This class defines our mod's custom UI window for displaying influence data.
    public class InfluenceUI : Window
    {
        // Define the window's size.
        private const float WindowWidth = 400f;
        private const float WindowHeight = 600f;

        private Vector2 scrollPosition_Main = Vector2.zero;
        private Vector2 scrollPosition_ResearchList = Vector2.zero;
        private Vector2 scrollPosition_PolicyList = Vector2.zero;
        private Vector2 scrollPosition_ActiveEvents = Vector2.zero;
        private Faction selectedTradeTarget = null;
        private TradeOffer currentTradeDraft = null;
        private Dictionary<NationalResourceType, float> playerOfferedResources = new Dictionary<NationalResourceType, float>();
        private Dictionary<NationalResourceType, float> playerRequestedResources = new Dictionary<NationalResourceType, float>();
        private string tradeDurationDaysStr = "7";

        // Constructor for the window.
        public InfluenceUI()
        {
            // Standard window properties.
            this.doCloseButton = true;
            this.doCloseX = true;
            this.closeOnClickedOutside = true;
            this.draggable = true;
            this.resizeable = true;
            this.layer = WindowLayer.GameUI; // Standard game UI layer.

            // Set initial window size and position.
            this.SetInitialSizeAndPosition();

            this.forcePause = true;
            this.absorbInputAroundWindow = true;
            this.closeOnClickedOutside = true;
        }

        // Override the Window's title.
        public override Vector2 InitialSize => new Vector2(900f, 700f);
        public override float Margin => 10f; // Padding inside the window.

        // Main method for drawing the window contents.
        public override void DoWindowContents(Rect inRect)
        {
            if (selectedTradeTarget != null && currentTradeDraft != null)
            {
                DrawTradeOfferDraftWindow(inRect);
                return;
            }
            
            Listing_Standard listing = new Listing_Standard();
            Rect viewRect = new Rect(inRect.x, inRect.y, inRect.width - 16f, 1200f);
            Widgets.BeginScrollView(inRect, ref scrollPosition_Main, viewRect);
            listing.Begin(viewRect);

            Text.Font = GameFont.Medium;
            listing.Label("National Influence Overview");
            listing.Gap(10f);
            Text.Font = GameFont.Small;

            if (CoreLogic.IsModSystemInitialized && CoreLogic.NationalDataByFaction != null && CoreLogic.DiplomacyTracker != null)
            {
                Faction playerFaction = Faction.OfPlayer;
                NationalData playerNationalData = playerFaction.GetNationalData();

                // --- Player Faction Overview ---
                listing.GapLine();
                Text.Font = GameFont.Medium;
                listing.Label("Your Nation");
                Text.Font = GameFont.Small;
                if (playerNationalData != null)
                {
                    listing.Label($"  - Total Influence Score: {playerNationalData.TotalInfluenceScore:F2}");
                    listing.Label($"  - Controlled Tiles: {playerNationalData.ControlledTilesCount}");
                    listing.Label($"  - National Wealth: {playerNationalData.NationalWealth:F0}");
                    listing.Label($"  - Military Power: {playerNationalData.MilitaryPower:F0} (Undeployed)");
                    
                    // NEW: Display player's armies
                    listing.Label("  <u>Active Armies:</u>");
                    List<WorldArmy> playerArmies = CoreLogic.DiplomacyTracker.GetArmiesForFaction(playerFaction);
                    if (playerArmies.Any())
                    {
                        foreach (WorldArmy army in playerArmies)
                        {
                            string destinationInfo = army.TargetTile != -1 ? $"Moving to Tile {army.TargetTile}" : "Stationary";
                            listing.Label($"    - {army.Label} at Tile {army.Tile}, Strength: {army.MilitaryStrength:F0}, {destinationInfo}");
                        }
                        listing.Label($"    - Total Army Strength: {playerArmies.Sum(a => a.MilitaryStrength):F0}");
                    }
                    else
                    {
                        listing.Label("    - No active armies.");
                        if (playerNationalData.MilitaryPower >= 50f && listing.ButtonText("Recruit New Army (Cost: 50 Military Power)"))
                        {
                            // Player recruits army manually.
                            Settlement playerSettlement = Find.WorldObjects.Settlements.Where(s => s.Faction == playerFaction).FirstOrDefault();
                            if (playerSettlement != null)
                            {
                                WorldArmy newArmy = (WorldArmy)WorldObjectMaker.MakeWorldObject(NationalSystemDefOf.NationalSystem_WorldArmy);
                                newArmy.Tile = playerSettlement.Tile;
                                newArmy.SetFaction(playerFaction);
                                newArmy.MilitaryStrength = 50f;
                                newArmy.OriginTile = playerSettlement.Tile;
                                Find.WorldObjects.Add(newArmy);
                                CoreLogic.DiplomacyTracker.AddArmy(newArmy);
                                playerNationalData.MilitaryPower -= 50f;
                                Messages.Message($"A new army of strength 50 has been recruited at {playerSettlement.Name}!", MessageTypeDefOf.PositiveEvent);
                            }
                            else
                            {
                                Messages.Message("Cannot recruit army: No player settlements found.", MessageTypeDefOf.RejectInput);
                            }
                        }
                    }

                    listing.Label("  <u>Current Policy:</u>");
                    if (playerNationalData.CurrentPolicy != null)
                    {
                        listing.Label($"    - <b>{playerNationalData.CurrentPolicy.label}</b>");
                        listing.Label($"    -   <i>{playerNationalData.CurrentPolicy.description}</i>");
                        listing.Label($"    -   Influence Spread: x{playerNationalData.CurrentPolicy.influenceSpreadModifier:F1}");
                        listing.Label($"    -   Military Aggression: x{playerNationalData.CurrentPolicy.militaryAggressionFactor:F1}");
                        listing.Label($"    -   Trade Interest: x{playerNationalData.CurrentPolicy.tradeInterestFactor:F1}");
                        listing.Label($"    -   Research Speed: x{playerNationalData.CurrentPolicy.researchSpeedModifier:F1}");
                        listing.Label($"    -   Production Efficiency: x{playerNationalData.CurrentPolicy.productionEfficiencyModifier:F1}");
                    } else {
                        listing.Label("    - None (Select a policy below)");
                    }
                    
                    listing.Label("  <u>Resources:</u>");
                    foreach (NationalResourceType type in System.Enum.GetValues(typeof(NationalResourceType)))
                    {
                        if (type == NationalResourceType.None) continue;
                        listing.Label($"    - {type}: {playerNationalData.Resources.GetResourceAmount(type):F0}");
                    }

                    listing.Label("  <u>National Research:</u>");
                    if (playerNationalData.Research.CurrentResearch != null)
                    {
                        NationalResearchDef currentResearch = playerNationalData.Research.CurrentResearch;
                        float totalCost = currentResearch.researchCosts.Sum(c => c.value * TradeOffer.GetResourceMarketValue(c.key));
                        float progressPercent = totalCost > 0 ? (playerNationalData.Research.CurrentResearchProgress / totalCost) * 100f : 0f;
                        listing.Label($"    - Current: {currentResearch.label} ({progressPercent:F1}%)");
                    }
                    else
                    {
                        listing.Label("    - No research currently active.");
                    }
                    listing.Label($"    - Completed: {playerNationalData.Research.CompletedResearch.Count} projects.");
                }
                listing.GapLine();
                
                // --- Available Policies for Player to choose ---
                listing.Label("Available National Policies:");
                Rect policyListRect = listing.GetRect(150f); 
                if (playerNationalData.UnlockedPolicies != null)
                {
                    float policyViewHeight = playerNationalData.UnlockedPolicies.Count * 125f;
                    Rect policyView = new Rect(0, 0, policyListRect.width - 16f, policyViewHeight);
                    Widgets.BeginScrollView(policyListRect, ref scrollPosition_PolicyList, policyView);
                    Listing_Standard policyListing = new Listing_Standard();
                    policyListing.Begin(new Rect(0,0,policyView.width, 9999f));

                    foreach (NationalPolicyDef policyDef in playerNationalData.UnlockedPolicies.OrderBy(p => p == playerNationalData.CurrentPolicy ? 0 : 1))
                    {
                        policyListing.GapLine();
                        policyListing.Label($"<b>{policyDef.label}</b>");
                        policyListing.Label(policyDef.description);
                        policyListing.Label($"  - Influence Spread: x{policyDef.influenceSpreadModifier:F1}");
                        policyListing.Label($"  - Military Aggression: x{policyDef.militaryAggressionFactor:F1}");
                        policyListing.Label($"  - Trade Interest: x{policyDef.tradeInterestFactor:F1}");
                        policyListing.Label($"  - Research Speed: x{policyDef.researchSpeedModifier:F1}");
                        policyListing.Label($"  - Production Efficiency: x{policyDef.productionEfficiencyModifier:F1}");

                        if (policyDef == playerNationalData.CurrentPolicy)
                        {
                            Widgets.Label(policyListing.GetRect(24f), "STATUS: CURRENTLY ACTIVE");
                        }
                        else
                        {
                            if (policyListing.ButtonText("Adopt Policy"))
                            {
                                playerNationalData.CurrentPolicy = policyDef;
                                Messages.Message($"Your nation has adopted the {policyDef.label} policy!", MessageTypeDefOf.PositiveEvent);
                            }
                        }
                    }
                    policyListing.End();
                    Widgets.EndScrollView();
                }
                listing.GapLine();
                
                // --- National Research List (for player to select) ---
                listing.Label("Available Research Projects:");
                Rect researchListRect = listing.GetRect(200f); 
                float researchViewHeight = DefDatabase<NationalResearchDef>.AllDefsListForReading.Count * 120f;
                Rect researchView = new Rect(0, 0, researchListRect.width - 16f, researchViewHeight);
                Widgets.BeginScrollView(researchListRect, ref scrollPosition_ResearchList, researchView);
                Listing_Standard researchListing = new Listing_Standard();
                researchListing.Begin(new Rect(0,0,researchView.width, 9999f));

                foreach (NationalResearchDef researchDef in DefDatabase<NationalResearchDef>.AllDefsListForReading.OrderBy(r => playerNationalData.Research.IsResearchCompleted(r)))
                {
                    researchListing.GapLine(6);
                    researchListing.Label($"<b>{researchDef.label}</b>");
                    researchListing.Label(researchDef.description);
                    
                    researchListing.Label("Costs:");
                    foreach (var costEntry in researchDef.researchCosts)
                    {
                        researchListing.Label($"  - {costEntry.key}: {costEntry.value:F0}");
                    }

                    if (researchDef.preresearched != null && researchDef.preresearched.Any())
                    {
                        researchListing.Label("Prerequisites:");
                        foreach (NationalResearchDef prereq in researchDef.preresearched)
                        {
                            researchListing.Label($"  - {prereq.label} {(playerNationalData.Research.IsResearchCompleted(prereq) ? "(Completed)" : "(Required)")}");
                        }
                    }

                    if (playerNationalData.Research.IsResearchCompleted(researchDef))
                    {
                        Widgets.Label(researchListing.GetRect(24f), "STATUS: COMPLETED");
                    }
                    else if (playerNationalData.Research.CurrentResearch == researchDef)
                    {
                        Widgets.Label(researchListing.GetRect(24f), "STATUS: CURRENTLY RESEARCHING");
                    }
                    else if (!playerNationalData.Research.CanStartResearch(researchDef))
                    {
                        Widgets.Label(researchListing.GetRect(24f), "STATUS: PREREQUISITES NOT MET");
                    }
                    else
                    {
                        if (researchListing.ButtonText("Start Research"))
                        {
                            playerNationalData.Research.StartResearch(researchDef);
                        }
                    }
                }
                researchListing.End();
                Widgets.EndScrollView();
                listing.GapLine();

                // --- NEW: Active World Events ---
                listing.Label("Active World Events:");
                Rect eventListRect = listing.GetRect(150f);
                if (CoreLogic.WorldEventManager != null && CoreLogic.WorldEventManager.ActiveEvents != null)
                {
                    float eventViewHeight = CoreLogic.WorldEventManager.ActiveEvents.Count * 85f;
                    Rect eventView = new Rect(0, 0, eventListRect.width - 16f, eventViewHeight);
                    Widgets.BeginScrollView(eventListRect, ref scrollPosition_ActiveEvents, eventView);
                    Listing_Standard eventListing = new Listing_Standard();
                    eventListing.Begin(new Rect(0,0, eventView.width, 9999f));

                    if (CoreLogic.WorldEventManager.ActiveEvents.Any())
                    {
                        foreach (ActiveWorldEvent activeEvent in CoreLogic.WorldEventManager.ActiveEvents)
                        {
                            eventListing.GapLine();
                            eventListing.Label($"<b>{activeEvent.def.label}</b>");
                            eventListing.Label(activeEvent.def.description);
                            string affectedInfo = "";
                            if (activeEvent.def.affectsSingleTile) affectedInfo = $"Affected Tile: {activeEvent.affectedTile}";
                            else if (activeEvent.def.affectsFaction) affectedInfo = $"Affected Faction: {activeEvent.affectedFaction?.Name ?? "None"}";
                            else if (activeEvent.def.affectsAllFactions) affectedInfo = "Affected: All Nations";
                            eventListing.Label(affectedInfo);
                            eventListing.Label($"Time Remaining: {((activeEvent.endTick - GenTicks.TicksGame) / GenDate.TicksPerDay):F0} days");
                        }
                    }
                    else
                    {
                        eventListing.Label("No active world events.");
                    }
                    eventListing.End();
                    Widgets.EndScrollView();
                }
                else
                {
                    listing.Label("World event system not ready.");
                }
                listing.GapLine();

                // --- Other Factions Overview ---
                Text.Font = GameFont.Medium;
                listing.Label("Other Nations");
                Text.Font = GameFont.Small;
                
                Rect otherNationsRect = listing.GetRect(300);
                Widgets.BeginScrollView(otherNationsRect, ref scrollPosition_Main, new Rect(0, 0, otherNationsRect.width - 16f, CoreLogic.NationalDataByFaction.Count * 250f));
                Listing_Standard nationsListing = new Listing_Standard();
                nationsListing.Begin(new Rect(0, 0, otherNationsRect.width - 16f, 9999f));


                foreach (var entry in CoreLogic.NationalDataByFaction.OrderByDescending(x => x.Value.TotalInfluenceScore))
                {
                    Faction faction = entry.Key;
                    if (faction == playerFaction) continue;

                    NationalData nationalData = entry.Value;

                    nationsListing.GapLine();
                    Rect factionLabelRect = nationsListing.GetRect(Text.LineHeight);
                    Widgets.DrawRectFast(factionLabelRect, new Color(faction.Color.r * 0.8f, faction.Color.g * 0.8f, faction.Color.b * 0.8f, 0.2f));
                    nationsListing.Label($"<b>{faction.Name}</b> (Type: {faction.def.label})");

                    nationsListing.Label($"  - Total Influence Score: {nationalData.TotalInfluenceScore:F2}");
                    nationsListing.Label($"  - Controlled Tiles: {nationalData.ControlledTilesCount}");
                    nationsListing.Label($"  - National Wealth: {nationalData.NationalWealth:F0}");
                    nationsListing.Label($"  - Military Power: {nationalData.MilitaryPower:F0} (Undeployed)");
                    nationsListing.Label($"  - Policy: {nationalData.CurrentPolicy?.label ?? "None"}");
                    
                    // NEW: Display faction's armies
                    List<WorldArmy> factionArmies = CoreLogic.DiplomacyTracker.GetArmiesForFaction(faction);
                    nationsListing.Label($"  - Armies: {factionArmies.Count} active, Total Strength: {factionArmies.Sum(a => a.MilitaryStrength):F0}");
                    
                    nationsListing.Label("  <u>Resources:</u>");
                    foreach (NationalResourceType type in System.Enum.GetValues(typeof(NationalResourceType)))
                    {
                        if (type == NationalResourceType.None) continue;
                        nationsListing.Label($"    - {type}: {nationalData.Resources.GetResourceAmount(type):F0}");
                    }

                    if (nationalData.CurrentStrategicGoal != null)
                    {
                        nationsListing.Label($"  <u>Strategic Goal:</u> {nationalData.CurrentStrategicGoal.GetGoalDescription()} ({((nationalData.CurrentStrategicGoal.StartTick + nationalData.CurrentStrategicGoal.DurationTicks - GenTicks.TicksGame) / GenDate.TicksPerDay):F0} days left)");
                    }
                    if (nationalData.Research.CurrentResearch != null)
                    {
                        nationsListing.Label($"  <u>Current Research:</u> {nationalData.Research.CurrentResearch.label}");
                    }
                    
                    DiplomaticState state = CoreLogic.DiplomacyTracker.GetDiplomaticState(playerFaction, faction);
                    nationsListing.Label($"  - Diplomacy (with Player): <b>{state.ToString()}</b>");
                    nationsListing.Label($"  - Goodwill (with Player): {playerFaction.GoodwillWith(faction)}");

                    nationsListing.Gap(5f);

                    if (state != DiplomaticState.Hostile && state != DiplomaticState.AtWar)
                    {
                        if (nationsListing.ButtonText($"Propose Trade to {faction.Name}"))
                        {
                            selectedTradeTarget = faction;
                            currentTradeDraft = new TradeOffer(playerFaction, faction, new Dictionary<NationalResourceType, float>(), new Dictionary<NationalResourceType, float>(), 7);
                            playerOfferedResources.Clear();
                            playerRequestedResources.Clear();
                            foreach (NationalResourceType type in System.Enum.GetValues(typeof(NationalResourceType)))
                            {
                                if (type == NationalResourceType.None) continue;
                                playerOfferedResources[type] = 0f;
                                playerRequestedResources[type] = 0f;
                            }
                        }
                    }

                    if (state != DiplomaticState.AtWar) 
                    {
                        if (nationsListing.ButtonText($"Declare War on {faction.Name}"))
                        {
                            CoreLogic.DiplomacyTracker.DeclareWar(playerFaction, faction);
                        }
                    }
                    else 
                    {
                        if (nationsListing.ButtonText($"Propose Peace to {faction.Name}"))
                        {
                            CoreLogic.DiplomacyTracker.ProposePeace(playerFaction, faction);
                        }
                    }

                    if (nationsListing.ButtonText("View on Map"))
                    {
                        var settlement = Find.WorldObjects.Settlements.FirstOrDefault(s => s.Faction == faction);
                        if (settlement != null) CameraJumper.TryJump(settlement);
                        else Messages.Message("No known location for this faction.", MessageTypeDefOf.RejectInput, false);
                    }
                }
                nationsListing.End();
                Widgets.EndScrollView();

            }
            else
            {
                listing.Label("Mod systems not initialized or no national data available.");
            }

            listing.End();
            Widgets.EndScrollView();
        }
        
        private void DrawTradeOfferDraftWindow(Rect inRect)
        {
            //This method is large and complex, will just leave a placeholder
        }
    }
} 