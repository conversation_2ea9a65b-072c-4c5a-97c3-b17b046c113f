using Verse;
using Rim<PERSON>orld; // For various UI elements like Listing_Standard, Widgets.
using UnityEngine; // For Rect, GUI.
using System.Linq; // For LINQ operations if needed.
using AIGen.NationalSystemMod.Common; // For CoreLogic access.
using AIGen.NationalSystemMod.Nation; // For NationalData access.
using AIGen.NationalSystemMod.World; // NEW: For DiplomaticState enum.
using AIGen.NationalSystemMod.Utils;
using AIGen.NationalSystemMod.Economy; // NEW: For TradeAgreement

namespace AIGen.NationalSystemMod.UI // New namespace for UI.
{
    // This class defines our mod's custom UI window for displaying influence data.
    public class InfluenceUI : Window
    {
        // Define the window's size.
        private const float WindowWidth = 400f;
        private const float WindowHeight = 600f;

        private Vector2 scrollPosition = Vector2.zero;

        // Constructor for the window.
        public InfluenceUI()
        {
            // Standard window properties.
            this.doCloseButton = true;
            this.doCloseX = true;
            this.closeOnClickedOutside = true;
            this.draggable = true;
            this.resizeable = true;
            this.layer = WindowLayer.GameUI; // Standard game UI layer.

            // Set initial window size and position.
            this.SetInitialSizeAndPosition();
        }

        // Override the Window's title.
        public override Vector2 InitialSize => new Vector2(500f, 700f);
        public override float Margin => 10f; // Padding inside the window.

        // Main method for drawing the window contents.
        public override void DoWindowContents(Rect inRect)
        {
            Listing_Standard listing = new Listing_Standard();
            
            Text.Font = GameFont.Medium;
            listing.Begin(inRect);
            listing.Label("National Influence Overview");
            listing.End();

            Rect mainRect = new Rect(inRect.x, inRect.y + 40, inRect.width, inRect.height - 40);
            
            if (!CoreLogic.IsModSystemInitialized || CoreLogic.NationalDataByFaction == null || CoreLogic.DiplomacyTracker == null)
            {
                listing.Begin(mainRect);
                listing.Label("Mod systems not initialized or no national data available.");
                listing.End();
                return;
            }

            var sortedFactions = CoreLogic.NationalDataByFaction
                .Where(kvp => kvp.Key != null && kvp.Value != null && !kvp.Key.IsPlayer && !kvp.Key.Hidden)
                .OrderByDescending(x => x.Value.TotalInfluenceScore)
                .ToList();

            Rect viewRect = new Rect(0f, 0f, mainRect.width - 16f, sortedFactions.Count * 220f); // Estimate height
            Widgets.BeginScrollView(mainRect, ref scrollPosition, viewRect);

            listing.Begin(viewRect);
            Faction playerFaction = Faction.OfPlayer;

            foreach (var entry in sortedFactions)
            {
                Faction faction = entry.Key;
                NationalData nationalData = entry.Value;

                listing.GapLine();

                Rect factionLabelRect = listing.GetRect(Text.LineHeight);
                if(faction.Color != default(Color))
                {
                    Widgets.DrawRectFast(factionLabelRect, new Color(faction.Color.r * 0.8f, faction.Color.g * 0.8f, faction.Color.b * 0.8f, 0.2f));
                }
                
                listing.Label($"<b>{faction.Name}</b> (Type: {faction.def.label})");

                listing.Label($"  - Total Influence Score: {nationalData.TotalInfluenceScore:F2}");
                listing.Label($"  - Controlled Tiles: {nationalData.ControlledTilesCount}");
                listing.Label($"  - National Wealth: {nationalData.NationalWealth:F0}");
                listing.Label($"  - Military Power: {nationalData.MilitaryPower:F0}");
                listing.Label($"  - Policy: {nationalData.CurrentPolicy?.label ?? "None"}");

                DiplomaticState state = CoreLogic.DiplomacyTracker.GetDiplomaticState(playerFaction, faction);
                listing.Label($"  - Diplomacy (with Player): <b>{state.ToString()}</b>");
                listing.Label($"  - Goodwill (with Player): {playerFaction.GoodwillWith(faction)}");

                // NEW: Display active trade agreement status.
                TradeAgreement activeTrade = CoreLogic.DiplomacyTracker.GetTradeAgreement(playerFaction, faction);
                if (activeTrade != null)
                {
                    listing.Label($"  - Trade Agreement: Active ({((activeTrade.StartTick + activeTrade.DurationTicks - GenTicks.TicksGame) / GenDate.TicksPerDay):F0} days left)");
                }
                else
                {
                    listing.Label($"  - Trade Agreement: None");
                }
                
                // Action buttons for player.
                listing.Gap(5f);

                // Propose Trade Button
                if (state != DiplomaticState.Hostile && state != DiplomaticState.AtWar)
                {
                    if (listing.ButtonText($"Propose Trade with {faction.Name}"))
                    {
                        CoreLogic.DiplomacyTracker.TryInitiateTradeAgreement(playerFaction, faction);
                    }
                }

                // Declare War / Propose Peace Buttons
                if (state != DiplomaticState.AtWar) // Not currently at war.
                {
                    if (listing.ButtonText($"Declare War on {faction.Name}", $"Declare a formal war. This will make relations hostile and allow for new conflict interactions. Your faction will be seen as the aggressor."))
                    {
                        CoreLogic.DiplomacyTracker.DeclareWar(playerFaction, faction);
                    }
                }
                else // Currently at war.
                {
                    if (listing.ButtonText($"Propose Peace to {faction.Name}", "Attempt to negotiate a peace treaty. Success is not guaranteed and depends on the state of the war and your relationship."))
                    {
                        CoreLogic.DiplomacyTracker.ProposePeace(playerFaction, faction);
                    }
                }
                
                if (listing.ButtonText("View on Map"))
                {
                    var settlement = Find.WorldObjects.Settlements.FirstOrDefault(s => s.Faction == faction);
                    if (settlement != null)
                    {
                        Find.WorldCameraDriver.JumpTo(settlement.Tile);
                    } else {
                        Messages.Message("No known location for this faction.", MessageTypeDefOf.RejectInput, false);
                    }
                }
            }
            
            listing.End();
            Widgets.EndScrollView();
        }
    }
} 