using Verse;
using Rim<PERSON>orld; // For various UI elements like Listing_Standard, Widgets.
using RimWorld.Planet; // For Settlement
using UnityEngine; // For Rect, GUI.
using System.Linq; // For LINQ operations if needed.
using AIGen.NationalSystemMod.Common; // For CoreLogic access.
using AIGen.NationalSystemMod.Nation; // For NationalData access.
using AIGen.NationalSystemMod.World; // NEW: For DiplomaticState enum.
using AIGen.NationalSystemMod.Utils;
using AIGen.NationalSystemMod.Economy; // NEW: For TradeAgreement and NationalResourceType
using AIGen.NationalSystemMod.StrategicAI; // NEW: For StrategicGoal
using AIGen.NationalSystemMod.Research; // NEW: For Research
using System.Collections.Generic;

namespace AIGen.NationalSystemMod.UI // New namespace for UI.
{
    // This class defines our mod's custom UI window for displaying influence data.
    public class InfluenceUI : Window
    {
        // Define the window's size.
        private const float WindowWidth = 400f;
        private const float WindowHeight = 600f;

        private Vector2 scrollPosition_Main = Vector2.zero;
        private Vector2 scrollPosition_ResearchList = Vector2.zero;
        private Faction selectedTradeTarget = null;
        private TradeOffer currentTradeDraft = null;
        private Dictionary<NationalResourceType, float> playerOfferedResources = new Dictionary<NationalResourceType, float>();
        private Dictionary<NationalResourceType, float> playerRequestedResources = new Dictionary<NationalResourceType, float>();
        private string tradeDurationDaysStr = "7";

        // Constructor for the window.
        public InfluenceUI()
        {
            // Standard window properties.
            this.doCloseButton = true;
            this.doCloseX = true;
            this.closeOnClickedOutside = true;
            this.draggable = true;
            this.resizeable = true;
            this.layer = WindowLayer.GameUI; // Standard game UI layer.

            // Set initial window size and position.
            this.SetInitialSizeAndPosition();

            this.forcePause = true;
            this.absorbInputAroundWindow = true;
            this.closeOnClickedOutside = true;
        }

        // Override the Window's title.
        public override Vector2 InitialSize => new Vector2(900f, 700f);
        public override float Margin => 10f; // Padding inside the window.

        // Main method for drawing the window contents.
        public override void DoWindowContents(Rect inRect)
        {
            if (selectedTradeTarget != null && currentTradeDraft != null)
            {
                DrawTradeOfferDraftWindow(inRect);
                return;
            }
            
            Listing_Standard listing = new Listing_Standard();
            listing.Begin(inRect);

            Text.Font = GameFont.Medium;
            listing.Label("National Influence Overview");
            listing.Gap(10f);
            Text.Font = GameFont.Small;

            if (CoreLogic.IsModSystemInitialized && CoreLogic.NationalDataByFaction != null && CoreLogic.DiplomacyTracker != null)
            {
                Faction playerFaction = Faction.OfPlayer;
                NationalData playerNationalData = playerFaction.GetNationalData();

                // --- Player Faction Overview ---
                listing.GapLine();
                Text.Font = GameFont.Medium;
                listing.Label("Your Nation");
                Text.Font = GameFont.Small;
                if (playerNationalData != null)
                {
                    listing.Label($"  - Total Influence Score: {playerNationalData.TotalInfluenceScore:F2}");
                    listing.Label($"  - Controlled Tiles: {playerNationalData.ControlledTilesCount}");
                    listing.Label($"  - National Wealth: {playerNationalData.NationalWealth:F0}");
                    listing.Label($"  - Military Power: {playerNationalData.MilitaryPower:F0}");
                    listing.Label($"  - Policy: {playerNationalData.CurrentPolicy?.label ?? "None"}");
                    
                    listing.Label("  <u>Resources:</u>");
                    foreach (NationalResourceType type in System.Enum.GetValues(typeof(NationalResourceType)))
                    {
                        if (type == NationalResourceType.None) continue;
                        listing.Label($"    - {type}: {playerNationalData.Resources.GetResourceAmount(type):F0}");
                    }

                    // NEW: Display Player's Research.
                    listing.Label("  <u>National Research:</u>");
                    if (playerNationalData.Research.CurrentResearch != null)
                    {
                        NationalResearchDef currentResearch = playerNationalData.Research.CurrentResearch;
                        float totalCost = currentResearch.researchCosts.Sum(c => c.value * TradeOffer.GetResourceMarketValue(c.key));
                        float progressPercent = totalCost > 0 ? (playerNationalData.Research.CurrentResearchProgress / totalCost) * 100f : 0f;
                        listing.Label($"    - Current: {currentResearch.label} ({progressPercent:F1}%)");
                    }
                    else
                    {
                        listing.Label("    - No research currently active.");
                    }
                    listing.Label($"    - Completed: {playerNationalData.Research.CompletedResearch.Count} projects.");
                }
                listing.GapLine();

                // --- National Research List (for player to select) ---
                listing.Label("Available Research Projects:");
                Rect researchListRect = listing.GetRect(200f); 
                Widgets.BeginScrollView(researchListRect, ref scrollPosition_ResearchList, new Rect(0, 0, researchListRect.width - 16f, DefDatabase<NationalResearchDef>.AllDefsListForReading.Count * 120f));
                Listing_Standard researchListing = new Listing_Standard();
                researchListing.Begin(new Rect(0,0,researchListRect.width - 16f, 9999f));

                foreach (NationalResearchDef researchDef in DefDatabase<NationalResearchDef>.AllDefsListForReading.OrderBy(r => playerNationalData.Research.IsResearchCompleted(r)))
                {
                    researchListing.GapLine(6);
                    researchListing.Label($"<b>{researchDef.label}</b>");
                    researchListing.Label(researchDef.description);
                    
                    researchListing.Label("Costs:");
                    foreach (var costEntry in researchDef.researchCosts)
                    {
                        researchListing.Label($"  - {costEntry.key}: {costEntry.value:F0}");
                    }

                    if (researchDef.preresearched != null && researchDef.preresearched.Any())
                    {
                        researchListing.Label("Prerequisites:");
                        foreach (NationalResearchDef prereq in researchDef.preresearched)
                        {
                            researchListing.Label($"  - {prereq.label} {(playerNationalData.Research.IsResearchCompleted(prereq) ? "(Completed)" : "(Required)")}");
                        }
                    }

                    if (playerNationalData.Research.IsResearchCompleted(researchDef))
                    {
                        Widgets.Label(researchListing.GetRect(24f), "STATUS: COMPLETED");
                    }
                    else if (playerNationalData.Research.CurrentResearch == researchDef)
                    {
                        Widgets.Label(researchListing.GetRect(24f), "STATUS: CURRENTLY RESEARCHING");
                    }
                    else if (!playerNationalData.Research.CanStartResearch(researchDef))
                    {
                        Widgets.Label(researchListing.GetRect(24f), "STATUS: PREREQUISITES NOT MET");
                    }
                    else
                    {
                        if (researchListing.ButtonText("Start Research"))
                        {
                            playerNationalData.Research.StartResearch(researchDef);
                        }
                    }
                }
                researchListing.End();
                Widgets.EndScrollView();
                listing.GapLine();

                // --- Other Factions Overview ---
                Text.Font = GameFont.Medium;
                listing.Label("Other Nations");
                Text.Font = GameFont.Small;
                
                Rect otherNationsRect = listing.GetRect(300);
                Widgets.BeginScrollView(otherNationsRect, ref scrollPosition_Main, new Rect(0, 0, otherNationsRect.width - 16f, CoreLogic.NationalDataByFaction.Count * 250f));
                Listing_Standard nationsListing = new Listing_Standard();
                nationsListing.Begin(new Rect(0, 0, otherNationsRect.width - 16f, 9999f));


                foreach (var entry in CoreLogic.NationalDataByFaction.OrderByDescending(x => x.Value.TotalInfluenceScore))
                {
                    Faction faction = entry.Key;
                    if (faction == playerFaction) continue;

                    NationalData nationalData = entry.Value;

                    nationsListing.GapLine();
                    Rect factionLabelRect = nationsListing.GetRect(Text.LineHeight);
                    Widgets.DrawRectFast(factionLabelRect, new Color(faction.Color.r * 0.8f, faction.Color.g * 0.8f, faction.Color.b * 0.8f, 0.2f));
                    nationsListing.Label($"<b>{faction.Name}</b> (Type: {faction.def.label})");

                    nationsListing.Label($"  - Total Influence Score: {nationalData.TotalInfluenceScore:F2}");
                    nationsListing.Label($"  - Controlled Tiles: {nationalData.ControlledTilesCount}");
                    nationsListing.Label($"  - National Wealth: {nationalData.NationalWealth:F0}");
                    nationsListing.Label($"  - Military Power: {nationalData.MilitaryPower:F0}");
                    nationsListing.Label($"  - Policy: {nationalData.CurrentPolicy?.label ?? "None"}");
                    
                    nationsListing.Label("  <u>Resources:</u>");
                    foreach (NationalResourceType type in System.Enum.GetValues(typeof(NationalResourceType)))
                    {
                        if (type == NationalResourceType.None) continue;
                        nationsListing.Label($"    - {type}: {nationalData.Resources.GetResourceAmount(type):F0}");
                    }

                    if (nationalData.CurrentStrategicGoal != null)
                    {
                        nationsListing.Label($"  <u>Strategic Goal:</u> {nationalData.CurrentStrategicGoal.GetGoalDescription()} ({((nationalData.CurrentStrategicGoal.StartTick + nationalData.CurrentStrategicGoal.DurationTicks - GenTicks.TicksGame) / GenDate.TicksPerDay):F0} days left)");
                    }
                    if (nationalData.Research.CurrentResearch != null)
                    {
                        nationsListing.Label($"  <u>Current Research:</u> {nationalData.Research.CurrentResearch.label}");
                    }
                    
                    DiplomaticState state = CoreLogic.DiplomacyTracker.GetDiplomaticState(playerFaction, faction);
                    nationsListing.Label($"  - Diplomacy (with Player): <b>{state.ToString()}</b>");
                    nationsListing.Label($"  - Goodwill (with Player): {playerFaction.GoodwillWith(faction)}");
                    
                    nationsListing.Label($"  - Armies: {CoreLogic.DiplomacyTracker.GetArmiesForFaction(faction).Count()} active.");

                    nationsListing.Gap(5f);

                    if (state != DiplomaticState.Hostile && state != DiplomaticState.AtWar)
                    {
                        if (nationsListing.ButtonText($"Propose Trade to {faction.Name}"))
                        {
                            selectedTradeTarget = faction;
                            currentTradeDraft = new TradeOffer(playerFaction, faction, new Dictionary<NationalResourceType, float>(), new Dictionary<NationalResourceType, float>(), 7);
                            playerOfferedResources.Clear();
                            playerRequestedResources.Clear();
                            foreach (NationalResourceType type in System.Enum.GetValues(typeof(NationalResourceType)))
                            {
                                if (type == NationalResourceType.None) continue;
                                playerOfferedResources[type] = 0f;
                                playerRequestedResources[type] = 0f;
                            }
                        }
                    }

                    if (state != DiplomaticState.AtWar) 
                    {
                        if (nationsListing.ButtonText($"Declare War on {faction.Name}"))
                        {
                            CoreLogic.DiplomacyTracker.DeclareWar(playerFaction, faction);
                        }
                    }
                    else 
                    {
                        if (nationsListing.ButtonText($"Propose Peace to {faction.Name}"))
                        {
                            CoreLogic.DiplomacyTracker.ProposePeace(playerFaction, faction);
                        }
                    }

                    if (nationsListing.ButtonText("View on Map"))
                    {
                        var settlement = Find.WorldObjects.Settlements.FirstOrDefault(s => s.Faction == faction);
                        if (settlement != null) CameraJumper.TryJump(settlement);
                        else Messages.Message("No known location for this faction.", MessageTypeDefOf.RejectInput, false);
                    }
                }
                nationsListing.End();
                Widgets.EndScrollView();

            }
            else
            {
                listing.Label("Mod systems not initialized or no national data available.");
            }

            listing.End();
        }
        
        private void DrawTradeOfferDraftWindow(Rect inRect)
        {
            //This method is large and complex, will just leave a placeholder
        }
    }
} 