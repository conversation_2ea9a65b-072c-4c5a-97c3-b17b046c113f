using Verse;
using Rim<PERSON>orld; // For various UI elements like Listing_Standard, Widgets.
using RimWorld.Planet; // For Settlement
using UnityEngine; // For Rect, GUI.
using System.Linq; // For LINQ operations if needed.
using AIGen.NationalSystemMod.Common; // For CoreLogic access.
using AIGen.NationalSystemMod.Nation; // For NationalData access.
using AIGen.NationalSystemMod.World; // NEW: For DiplomaticState enum.
using AIGen.NationalSystemMod.Utils;
using AIGen.NationalSystemMod.Economy; // NEW: For TradeAgreement and NationalResourceType
using AIGen.NationalSystemMod.StrategicAI; // NEW: For StrategicGoal

namespace AIGen.NationalSystemMod.UI // New namespace for UI.
{
    // This class defines our mod's custom UI window for displaying influence data.
    public class InfluenceUI : Window
    {
        // Define the window's size.
        private const float WindowWidth = 400f;
        private const float WindowHeight = 600f;

        private Vector2 scrollPosition = Vector2.zero;

        // Constructor for the window.
        public InfluenceUI()
        {
            // Standard window properties.
            this.doCloseButton = true;
            this.doCloseX = true;
            this.closeOnClickedOutside = true;
            this.draggable = true;
            this.resizeable = true;
            this.layer = WindowLayer.GameUI; // Standard game UI layer.

            // Set initial window size and position.
            this.SetInitialSizeAndPosition();

            this.forcePause = true;
            this.absorbInputAroundWindow = true;
            this.closeOnClickedOutside = true;
        }

        // Override the Window's title.
        public override Vector2 InitialSize => new Vector2(700f, 800f);
        public override float Margin => 10f; // Padding inside the window.

        // Main method for drawing the window contents.
        public override void DoWindowContents(Rect inRect)
        {
            Listing_Standard listing = new Listing_Standard();
            listing.Begin(inRect);

            Text.Font = GameFont.Medium;
            listing.Label("National Influence Overview");
            listing.Gap(10f);
            Text.Font = GameFont.Small;

            if (CoreLogic.IsModSystemInitialized && CoreLogic.NationalDataByFaction != null && CoreLogic.DiplomacyTracker != null)
            {
                Faction playerFaction = Faction.OfPlayer;

                foreach (var entry in CoreLogic.NationalDataByFaction.OrderByDescending(x => x.Value.TotalInfluenceScore))
                {
                    Faction faction = entry.Key;
                    NationalData nationalData = entry.Value;
                    if (nationalData == null || faction.Hidden || !faction.def.humanlikeFaction) continue;

                    listing.GapLine();

                    Rect factionLabelRect = listing.GetRect(Text.LineHeight);
                    GUI.color = faction.Color;
                    Widgets.DrawRectFast(factionLabelRect, new Color(faction.Color.r * 0.8f, faction.Color.g * 0.8f, faction.Color.b * 0.8f, 0.2f));
                    GUI.color = Color.white;
                    listing.Label($"<b>{faction.Name}</b> (Type: {faction.def.label})");

                    listing.Label($"  - Total Influence Score: {nationalData.TotalInfluenceScore:F2}");
                    listing.Label($"  - Controlled Tiles: {nationalData.ControlledTilesCount}");
                    listing.Label($"  - National Wealth: {nationalData.NationalWealth:F0}");
                    listing.Label($"  - Military Power: {nationalData.MilitaryPower:F0}");
                    listing.Label($"  - Policy: {nationalData.CurrentPolicy?.label ?? "None (Player)"}");

                    // NEW: Display Resources.
                    listing.Label("  <u>Resources:</u>");
                    foreach (NationalResourceType type in System.Enum.GetValues(typeof(NationalResourceType)))
                    {
                        if (type == NationalResourceType.None) continue;
                        listing.Label($"    - {type}: {nationalData.Resources.GetResourceAmount(type):F0}");
                    }

                    // NEW: Display Strategic Goal for AI factions.
                    if (!faction.IsPlayer && nationalData.CurrentStrategicGoal != null)
                    {
                        listing.Label($"  <u>Strategic Goal:</u>");
                        listing.Label($"    - {nationalData.CurrentStrategicGoal.GetGoalDescription()} ({((nationalData.CurrentStrategicGoal.StartTick + nationalData.CurrentStrategicGoal.DurationTicks - GenTicks.TicksGame) / GenDate.TicksPerDay):F0} days left)");
                    }


                    if (faction != playerFaction)
                    {
                        DiplomaticState state = CoreLogic.DiplomacyTracker.GetDiplomaticState(playerFaction, faction);
                        listing.Label($"  - Diplomacy (with Player): <b>{state.ToString()}</b>");
                        listing.Label($"  - Goodwill (with Player): {playerFaction.GoodwillWith(faction)}");

                        TradeOffer activeTrade = CoreLogic.DiplomacyTracker.GetTradeAgreement(playerFaction, faction);
                        if (activeTrade != null && GenTicks.TicksGame < activeTrade.ExpiryTick)
                        {
                            listing.Label($"  - Trade Agreement: Active ({((activeTrade.ExpiryTick - GenTicks.TicksGame) / GenDate.TicksPerDay):F0} days left)");
                        }
                        else
                        {
                            listing.Label($"  - Trade Agreement: None");
                        }
                        
                        // Action buttons for player (Trade, War, Peace).
                        if (playerFaction.IsPlayer)
                        {
                            listing.Gap(5f);

                            if (state != DiplomaticState.Hostile && state != DiplomaticState.AtWar)
                            {
                                if (listing.ButtonText($"Propose Trade with {faction.Name}"))
                                {
                                    if (CoreLogic.DiplomacyTracker.GetTradeAgreement(playerFaction, faction) == null)
                                    {
                                        CoreLogic.DiplomacyTracker.TryInitiateTradeAgreement(playerFaction, faction);
                                    } else {
                                        Messages.Message($"You already have an active trade agreement with {faction.Name}.", MessageTypeDefOf.RejectInput, false);
                                    }
                                }
                            }

                            if (state != DiplomaticState.AtWar) 
                            {
                                if (listing.ButtonText($"Declare War on {faction.Name}"))
                                {
                                    CoreLogic.DiplomacyTracker.DeclareWar(playerFaction, faction);
                                }
                            }
                            else 
                            {
                                if (listing.ButtonText($"Propose Peace to {faction.Name}"))
                                {
                                    CoreLogic.DiplomacyTracker.ProposePeace(playerFaction, faction);
                                }
                            }
                        }
                    }

                    // View on Map Button (remains)
                    if (listing.ButtonText("View on Map"))
                    {
                        Settlement settlement = Find.WorldObjects.Settlements.Where(s => s.Faction == faction).FirstOrDefault();
                        if (settlement != null)
                        {
                            Find.WorldSelector.Select(settlement);
                        }
                        else
                        {
                            Messages.Message("No known location for this faction.", MessageTypeDefOf.RejectInput, false);
                        }
                    }
                }
            }
            else
            {
                listing.Label("Mod systems not initialized or no national data available.");
            }

            listing.End();
        }
    }
} 