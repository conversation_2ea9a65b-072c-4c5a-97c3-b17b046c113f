---
description: 
globs: 
alwaysApply: true
---

RimWorld_NationalSystem_Mod/
├── About/
│   └── About.xml             // Mod information (name, version, description, compatibility, etc.)
├── Common/                   // Globally used code or utilities
│   └── CoreLogic.cs          // Core mod logic (likely where most AI-generated code will reside)
├── Defs/                     // XML definition files (RimWorld data definitions)
│   ├── FactionDefs/          // National faction definitions (new nation types, existing faction modifications)
│   │   └── NationalFactionDefs.xml
│   ├── ThingDefs_Buildings/  // National system-related building definitions (border outposts, capital buildings, etc.)
│   │   └── NationalBuildings.xml
│   ├── HediffDefs/           // Hediff definitions (related to national instability, etc.)
│   ├── IncidentDefs/         // Event definitions (nation declaration events, diplomatic events, rebellion events, etc.)
│   │   └── NationalIncidents.xml
│   ├── ResearchProjectDefs/  // Nation-related research project definitions (diplomatic tech, administrative tech, etc.)
│   │   └── NationalResearch.xml
│   └── WorldObjectDefs/      // World map objects related to nations (armies, special strongholds, etc.)
│       └── NationalWorldObjects.xml
├── Languages/                // Translation files
│   └── English/
│       └── Keyed/
│           └── NationalSystem_Keys.xml
│       └── Strings/
│           └── NationalSystem_Strings.xml
├── Patches/                  // Files for patching existing RimWorld data (XML)
│   └── FactionPatch.xml      // Add national system properties to existing factions, etc.
│   └── WorldMapPatch.xml     // Patch world map-related data
├── Textures/                 // Image files used by the mod
│   ├── UI/                   // UI icons, backgrounds, etc.
│   └── World/                // Textures for world map display (border line textures, etc.)
├── Source/                   // C# source code (primarily to be generated by AI)
│   ├── NationalSystemMod.cs  // Mod entry point and initialization
│   ├── Managers/             // Various system managers (DiplomacyManager, EconomyManager, MilitaryManager)
│   │   ├── DiplomacyManager.cs
│   │   ├── EconomyManager.cs
│   │   └── MilitaryManager.cs
│   ├── Components/           // Components attached to world objects or pawns
│   │   └── NationalInfluenceComp.cs // Influence component for cities or nations
│   ├── UI/                   // UI-related code (nation info window, diplomacy window, etc.)
│   │   ├── NationalInfoWindow.cs
│   │   └── DiplomacyWindow.cs
│   ├── World/                // World map-related logic (drawing borders, world map movement, etc.)
│   │   └── NationalBorderDrawer.cs
│   │   └── WorldInfluenceMap.cs
│   └── Utilities/            // Common utility functions
│       └── OptimizationUtils.cs // Optimization utilities (periodic update scheduler, etc.)
└── Mod.dll                   // Compiled mod file (final output from AI compilation)

Note:

The .cs files in the Source/ folder are the C# code files that Cursor AI will generate.
The .xml files in the Defs/ folder define mod elements according to RimWorld's data structure. It would be beneficial if Cursor AI could understand and populate these XML files.
Mod.dll is the file generated when the mod is finally built


# Project: RimWorld National System Mod

## 1. Project Goal

The ultimate goal of this project is to introduce a deep national system to RimWorld, similar to Paradox games (e.g., Europa Universalis, Crusader Kings) and Kenshi. Players should experience a richer, more dynamic RimWorld by evolving their colony into a nation, engaging in diplomacy, warfare, and economic activities to conquer the world, or managing colonies and vassals to secure resources. The mod will integrate the existing RimWorld faction system into the national system to make it feel more alive.

## 2. Core Features

### 2.1. Nation Concept and Creation/Destruction
-   **Player Nation:** The player's colony starts as a city. Through the Royalty DLC's title system, it can become a vassal within an empire, or, upon meeting certain conditions, declare itself a nation.
-   **Nation Declaration Penalty:** When a player declares a nation, initially, due to a lack of legitimacy, the goodwill of neighboring nations or the empire may decrease, leading to danger.
-   **Legitimacy Acquisition Mechanisms:**
    -   **Accumulating Prestige/Authority:** Gaining prestige based on settlement development (population, assets, tech level).
    -   **Diplomatic Recognition:** Acquiring recognition through diplomatic events by maintaining high goodwill or cooperating with specific factions (e.g., repelling large threats).
    -   **Internal Stability:** Maintaining high internal stability within the colony.
-   **Nation Scale/Government Types:** Nations can range from small city-states to large empires, supporting various government types such as theocracy, republic, and administration (initially, monarchy, republic, and tribal will be prioritized).
-   **Nation Destruction:** A nation is destroyed when all its cities are captured, or when it loses a war whose objective was national conquest.

### 2.2. Border System
-   **Visual Display:** Nation borders will be visually represented as lines on the world map. Tribal factions will not have their borders displayed.
-   **Expansion/Contraction:** Borders will expand/contract based on a city's 'Influence' (up to 8 tiles per city).
    -   **Influence Gain:** Gained through city development, quest completion, cultural influence spread, special building construction, troop garrisons, etc.
    -   **Influence Loss:** Lost through city capture, internal instability, diplomatic friction with neighbors, troop losses, etc.
    -   **Influence Effects:** Beyond border expansion, influence can affect cultural/economic sway over nearby independent settlements, priority access to local resources, and even the erosion of other nations' influence.
-   **Rules/Effects within Borders:**
    -   **Resource Bonuses/Penalties:** Increased harvest rates or production bonuses for specific resources within borders (with penalties in war-torn areas).
    -   **Stability/Security:** Higher overall security within borders, leading to fewer raids, safer traders, and visitors (excluding unstable border regions).
    -   **Movement Speed/Safety:** Increased movement speed for colonists or caravans within one's own territory, reduced risk from hostile wildlife or raider factions.
    -   **Special Buildings/Policies:** Ability to construct special buildings (e.g., border patrol outposts, customs offices) or apply specific laws (e.g., border trade restrictions, immigration limits) only within one's borders.
    -   **Cultural Assimilation:** Gradual assimilation of pawns from other cultures residing within borders, potentially affecting long-term stability or productivity in those regions.
-   **Cross-Border Movement/Invasion:**
    -   **Right of Passage/Toll:** To move through another nation's territory, a right of passage must be obtained (if allied or highly amicable) or a toll paid. If goodwill is low, even a toll may be refused, and forced movement could lead to attack.
    -   **Initiating Invasion:** Invasions can be declared through a leader's speech or by selecting an attack target on the world map for a surprise assault.
-   **Faction City Placement:** All non-tribal faction cities should be grouped together on the world map.

### 2.3. National System Key Elements

#### Diplomacy
-   **Relationship Changes:** Determined by goodwill, leading to alliances, hostilities, trade agreements, etc. Cultural/ideological overlap (from Ideology DLC) will provide basic/significant goodwill bonuses.
-   **Interactions:**
    -   **Independent of Goodwill:** Sending envoys, declaring war, requesting/offering resources, requesting emergency aid (during disasters).
    -   **Goodwill-Dependent:** Alliance proposals/formations, trade agreements, secret pacts, technology sharing, cultural exchange programs.

#### Economy
-   **National Treasury:** Manage national finances using silver or a new currency unit.
    -   **Income:** Partial resource acquisition from colonies/vassals (based on climate/terrain), taxes (silver).
    -   **Expenditures:** Military upkeep, research investment, social welfare/infrastructure investment, diplomatic/espionage activities.
-   **National Resource Management:**
    -   **Core Resources:** Track national production/consumption of core resources like food, steel, components, etc.
    -   **Strategic Resources:** Introduce strategic resources unique to certain nations, encouraging diplomatic/military competition.
-   **Inter-National Trade:** Utilize RimWorld's basic trade system. Introduce trade agreements (customs exemptions, increased trade volume), embargoes/blockades, and trade hubs.

#### Politics/Internal Affairs
-   **Government Types:** Player can choose Monarchy or Republic initially. Each type will have unique pros, cons, and characteristics.
-   **Internal Stability:** A stability indicator (e.g., 0-100%) based on pawn mood, food supply, security, ideological alignment, leader popularity.
    -   **Low Stability Effects:** Increased rebellion chance, higher crime rate, reduced productivity, increased colony discontent.
-   **National Policies/Laws:** Set policies across categories like economy, military, society, research, and diplomacy. Changing policies will incur penalties such as cooldowns, stability drops, resource costs, and increased discontent.

#### Military
-   **National Unit Management:** On the world map, 'armies' or 'units' will be abstractly managed (combat power, movement speed, upkeep).
    -   **Troop Production/Conscription:** Produce/conscript troops based on national population, military buildings, and conscription policies.
    -   **Upkeep:** Silver or specific resources (food, apparel) consumed for troop maintenance.
-   **Inter-National Warfare:**
    -   **War Goals:** Clear war goals (e.g., capture specific city, secure resources, nation destruction, vassalization, reparations).
    -   **Conduct:** Abstract unit movement and engagements on the world map (simple win/loss, damage calculation). For major locations (cities), transition to actual RimWorld combat maps.
    -   **Siege/Occupation:** Besieging/occupying cities to disrupt enemy resource production and lower stability.
    -   **Peace Negotiations:** Peace negotiation events during or after a war, allowing various terms (territory return, reparations, alliances, vassalization).
    -   **Casus Belli:** Introduce a 'casus belli' system; declaring war without a valid reason incurs international disapproval (goodwill drop) or intervention from other nations.
-   **Defensive Structures/Strategic Points:** Ability to construct special defensive buildings (fortresses) in strategic locations on the world map (mountains, rivers, narrow passes). Cities will have inherent defensive bonuses.

#### Technology/Culture
-   **National Tech Advancement:** Research speed bonuses based on national policies, buildings, population, education level. Unique technologies exclusive to certain government types or ideologies.
-   **Culture/Traits:** Utilize the Ideology DLC. Each nation's dominant ideology/belief will influence its policies, pawn traits, preferred technologies, and diplomatic relations. Cultural influence can lead to assimilation of nearby regions.

## 3. Integration with Core RimWorld Systems
-   **Existing Faction Integration:** Existing 'friendly factions' and 'the Empire' will be classified as 'nations' in this mod, with all national system elements applied to them.
-   **Tribal Factions:** Remain independent entities without borders. Subject to influence from neighboring nations or potential colonization/vassalization.
-   **Independent Settlements:** Can be absorbed, developed into colonies, or targeted for conquest based on national influence.
-   **Event Interlinkage:** Existing faction raids/trade visits will vary in frequency, scale, and purpose based on national diplomatic relations.
-   **Colony-Nation Relationship:** The player's colony will act as the core city of their nation (capital, colonial/vassal city). Colony development directly contributes to national development.
-   **World Map Implementation:** All discussed systems (borderlines, grouped cities, influence areas) will be visually implemented on the world map.

## 4. Optimization Principles (Crucial!)
This mod prioritizes **minimizing tick generation to prevent game performance degradation**. Cursor AI must adhere to the following principles when implementing all logic:

-   **Event-Driven Processing:** Instead of checking all elements every tick, design calculations to be performed **only when specific events occur**.
-   **Periodic Updates:** Avoid unnecessary calculations every tick. Schedule them to run **once per in-game day or week**.
-   **Change Detection:** Trigger subsequent actions only when a data value has actually changed.
-   **Efficient Data Structures:** Store minimal data; derive complex information only when needed.
-   **World Map Data Optimization:** Instead of storing data for every world map tile, store **border line data or influence areas centered on key cities** and visualize them as needed.
-   **Abstract Data for Units:** Military units on the world map should be abstract (e.g., combat power) rather than simulating individual pawns' movements every tick.
-   **Profiling and Improvement:** After implementing each feature, profile performance to identify and resolve tick-generating bottlenecks.

## 5. Instructions for Cursor AI
-   Generate code and write XML files according to the provided folder structure.
-   All C# code must adhere to RimWorld modding standards.
-   XML definition files (`Defs/`) must maintain consistency with RimWorld's existing XML structure.
-   When implementing any feature, **always prioritize the optimization principles** listed above. Actively seek and apply methods to minimize tick generation.
-   Please propose pseudocode, main class, and method structures first, and proceed with actual code generation upon approval.
-   If you encounter parts that are difficult to implement or optimize, suggest alternatives or request further discussion.

## 6. Development Environment/Tools
-   **Game:** RimWorld (utilizing Royalty DLC, Ideology DLC)
-   **Mod Development Tool:** Cursor AI (Vibe Coding)



-   **Language:** C#, XML