using Verse;
using RimWorld.Planet;
using System.Collections.Generic;
using System.Linq;
using NationalSystemMod.Economy;
using NationalSystemMod.Nation;
using NationalSystemMod.Defs;
using NationalSystemMod.Common;

namespace NationalSystemMod.World
{
    public class WorldEventManager : IExposable
    {
        private List<ActiveWorldEvent> _activeEvents = new List<ActiveWorldEvent>();

        // For periodic event spawning.
        private const int EVENT_SPAWN_INTERVAL_TICKS = GenDate.TicksPerDay * 5; // Every 5 days.
        private int _lastEventSpawnTick = -1;

        public List<ActiveWorldEvent> ActiveEvents => _activeEvents;

        public WorldEventManager() { }

        public void WorldEventTick()
        {
            // Clean up expired events.
            _activeEvents.RemoveAll(e => !e.IsActive);

            // Tick active events.
            foreach (ActiveWorldEvent activeEvent in _activeEvents)
            {
                activeEvent.EventTick();
            }

            // Try to spawn new events periodically.
            if (_lastEventSpawnTick == -1)
            {
                _lastEventSpawnTick = GenTicks.TicksGame;
            }
            
            if (GenTicks.TicksGame >= _lastEventSpawnTick + EVENT_SPAWN_INTERVAL_TICKS)
            {
                TrySpawnRandomEvent();
                _lastEventSpawnTick = GenTicks.TicksGame;
            }
        }

        private void TrySpawnRandomEvent()
        {
            // Get all possible event defs.
            List<WorldEventDef> possibleEvents = DefDatabase<WorldEventDef>.AllDefsListForReading;

            // Filter out events that can't currently happen (e.g., no valid tiles/factions).
            List<WorldEventDef> validEvents = new List<WorldEventDef>();
            foreach (WorldEventDef eventDef in possibleEvents)
            {
                if (CanEventSpawn(eventDef))
                {
                    validEvents.Add(eventDef);
                }
            }

            if (!validEvents.Any())
            {
                //Log.Message("[NationalSystemMod] No valid world events to spawn.");
                return;
            }

            // Weighted random selection.
            WorldEventDef chosenEvent = validEvents.RandomElementByWeight(e => e.commonality);

            // Determine affected entities based on event type.
            int affectedTile = -1;
            Faction affectedFaction = null;

            if (chosenEvent.affectsSingleTile)
            {
                affectedTile = FindValidTileForEvent(chosenEvent);
                if (affectedTile == -1) return; // Couldn't find a valid tile.
            }
            else if (chosenEvent.affectsFaction)
            {
                affectedFaction = FindValidFactionForEvent(chosenEvent);
                if (affectedFaction == null) return; // Couldn't find a valid faction.
            }
            // If affectsAllFactions, no specific tile/faction needed.

            // Create and add the new active event.
            int durationTicks = Rand.Range(chosenEvent.minDurationDays, chosenEvent.maxDurationDays) * GenDate.TicksPerDay;
            ActiveWorldEvent newEvent = new ActiveWorldEvent(chosenEvent, durationTicks, affectedTile, affectedFaction);
            _activeEvents.Add(newEvent);
            Log.Message($"[NationalSystemMod] Spawned new world event: {chosenEvent.label}");

            // NEW: Handle custom effects immediately upon event creation.
            HandleCustomEventEffects(newEvent);
        }

        private bool CanEventSpawn(WorldEventDef def)
        {
            // Basic check if a target can be found.
            if (def.affectsSingleTile && FindValidTileForEvent(def) == -1) return false;
            if (def.affectsFaction && FindValidFactionForEvent(def) == null) return false;
            
            // Add more complex spawning conditions here later, e.g.,
            // - Event requires specific game stage.
            // - Event cannot happen if another specific event is active.
            return true;
        }

        private int FindValidTileForEvent(WorldEventDef eventDef)
        {
            List<int> validTiles = new List<int>();
            // Simple example: Find a random land tile.
            // More complex: Filter by WorldInfluenceMap ownership, resource type, etc.
            for (int tileID = 0; tileID < Find.WorldGrid.TilesCount; tileID++)
            {
                if (Find.WorldGrid[tileID].WaterCovered) continue; // Skip water tiles.
                
                // Example for "OwnedByFaction" filter:
                if (eventDef.TileTagsFilter != null && eventDef.TileTagsFilter.Contains("OwnedByFaction"))
                {
                    if (CoreLogic.WorldInfluenceMap.InfluenceDataByTile.TryGetValue(tileID, out var tileData) && tileData.faction != null)
                    {
                         validTiles.Add(tileID);
                    }
                }
                else // If no specific filter or other filters, add any non-water tile.
                {
                    validTiles.Add(tileID);
                }
            }
            return validTiles.Any() ? validTiles.RandomElement() : -1;
        }

        private Faction FindValidFactionForEvent(WorldEventDef eventDef)
        {
            List<Faction> validFactions = new List<Faction>();
            foreach (Faction faction in Find.FactionManager.AllFactionsListForReading)
            {
                if (!faction.IsPlayer && !faction.IsHidden && faction.def.isCivil)
                {
                    // Filter by faction type (if specified).
                    if (eventDef.FactionDefNamesFilter != null && eventDef.FactionDefNamesFilter.Any() && !eventDef.FactionDefNamesFilter.Contains(faction.def.defName))
                    {
                        continue;
                    }

                    // Check goodwill range (relative to player or other factions, depends on event design).
                    // For simplicity, let's assume it's player-goodwill for now if targeting AI.
                    if (eventDef.targetGoodwillRange.min <= faction.RelationWith(Faction.OfPlayer).goodwill && 
                        faction.RelationWith(Faction.OfPlayer).goodwill <= eventDef.targetGoodwillRange.max)
                    {
                         validFactions.Add(faction);
                    }
                }
            }
            return validFactions.Any() ? validFactions.RandomElement() : null;
        }


        // NEW: Handles custom effects of an event.
        private void HandleCustomEventEffects(ActiveWorldEvent activeEvent)
        {
            if (activeEvent.def.CustomEffectTags == null || !activeEvent.def.CustomEffectTags.Any()) return;

            foreach (string tag in activeEvent.def.CustomEffectTags)
            {
                switch (tag)
                {
                    case "SPAWN_REBEL_ARMY":
                        SpawnRebelArmy(activeEvent);
                        break;
                    case "GLOBAL_RESEARCH_BOOST":
                        // This will be handled in NationalResearchManager, but we can log here.
                        Log.Message($"[NationalSystemMod.EventEffect] Global Research Boost triggered by {activeEvent.def.label}.");
                        break;
                    // Add more custom effects here as needed.
                }
            }
        }

        // NEW: Logic to spawn a rebel army.
        private void SpawnRebelArmy(ActiveWorldEvent activeEvent)
        {
            if (activeEvent.affectedTile == -1) return;

            InfluenceTileData tileData = CoreLogic.WorldInfluenceMap.GetInfluenceData(activeEvent.affectedTile);
            Faction ownerFaction = tileData?.faction;
            if (ownerFaction == null)
            {
                Log.Warning($"[NationalSystemMod.EventEffect] Cannot spawn rebel army: Affected tile {activeEvent.affectedTile} has no owner.");
                return;
            }

            // Find or create a rebel faction
            Faction rebelFaction = Find.FactionManager.AllFactionsListForReading
                .FirstOrDefault(f => f.def.defName.Contains("Rebel") && !f.IsPlayer && !f.IsHidden);
            
            if (rebelFaction == null)
            {
                // Create a temporary "rebel" faction if one doesn't exist
                rebelFaction = FactionGenerator.NewGeneratedFaction(FactionDefOf.OutlanderHostile);
                rebelFaction.Name = $"Rebels of {ownerFaction.Name}";
                Find.FactionManager.Add(rebelFaction);
                Log.Message($"[NationalSystemMod.EventEffect] Created temporary rebel faction: {rebelFaction.Name}");
            }
            
            // Set hostile relationship with owner
            if (rebelFaction.RelationWith(ownerFaction) == null)
            {
                rebelFaction.SetRelationDirect(ownerFaction, FactionRelationKind.Hostile, false);
            }
            else
            {
                rebelFaction.RelationWith(ownerFaction).kind = FactionRelationKind.Hostile;
                rebelFaction.RelationWith(ownerFaction).goodwill = -100;
            }
            
            // Spawn the army
            WorldArmy rebelArmy = (WorldArmy)WorldObjectMaker.MakeWorldObject(NationalSystemDefOf.NationalSystem_WorldArmy);
            rebelArmy.Tile = activeEvent.affectedTile;
            rebelArmy.SetFaction(rebelFaction);
            rebelArmy.MilitaryStrength = Rand.Range(30f, 70f); // Moderate strength
            rebelArmy.OriginTile = activeEvent.affectedTile;
            Find.WorldObjects.Add(rebelArmy);

            Log.Message($"[NationalSystemMod.EventEffect] Rebel army ({rebelArmy.MilitaryStrength:F0}) spawned at tile {activeEvent.affectedTile} for {ownerFaction.Name}.");
            Messages.Message($"A local rebellion has escalated in {ownerFaction.Name}'s territory at tile {activeEvent.affectedTile}!", MessageTypeDefOf.NegativeEvent);
        }

        public void ExposeData()
        {
            Scribe_Collections.Look(ref _activeEvents, "activeEvents", LookMode.Deep);
            Scribe_Values.Look(ref _lastEventSpawnTick, "lastEventSpawnTick", -1);

            if (Scribe.mode == LoadSaveMode.PostLoadInit && _activeEvents == null)
            {
                _activeEvents = new List<ActiveWorldEvent>();
            }
        }
    }
} 