using Verse;
using RimWorld;
using RimWorld.Planet;
using UnityEngine; // For Mathf
using System.Collections.Generic;
using System.Linq;
using AIGen.NationalSystemMod.Common;
using AIGen.NationalSystemMod.Nation;
using AIGen.NationalSystemMod.Utils;
using AIGen.NationalSystemMod.Economy; // NEW: For TradeAgreement

namespace AIGen.NationalSystemMod.World
{
    // Define an enum for our custom diplomatic states.
    public enum DiplomaticState
    {
        Neutral,
        Allied,
        Rival,
        Hostile,
        AtWar // NEW: Explicit state for active conflict.
    }

    public class FactionDiplomacyTracker : WorldComponent
    {
        // Dictionary to store the diplomatic state between pairs of factions.
        // Key: Faction A, Value: Dictionary<Faction B, DiplomaticState>
        private Dictionary<Faction, Dictionary<Faction, DiplomaticState>> _diplomaticStates;
        private Dictionary<Faction, Dictionary<Faction, TradeAgreement>> _activeTradeAgreements; // NEW: Track active trade agreements.

        // Last tick when diplomatic states were re-evaluated.
        private const int DIPLOMACY_EVALUATION_INTERVAL_TICKS = GenDate.TicksPerDay * 2; // Every 2 days.
        private int _lastDiplomacyEvaluationTick = -1;

        public FactionDiplomacyTracker(RimWorld.Planet.World world) : base(world)
        {
            _diplomaticStates = new Dictionary<Faction, Dictionary<Faction, DiplomaticState>>();
            _activeTradeAgreements = new Dictionary<Faction, Dictionary<Faction, TradeAgreement>>(); // Initialize.
        }

        public override void ExposeData()
        {
            base.ExposeData();
            
            List<DiplomacyEntry> diplomacyEntries = new List<DiplomacyEntry>();
            if (Scribe.mode == LoadSaveMode.Saving)
            {
                // Prevent saving duplicates by only saving one side of the relationship
                var savedPairs = new HashSet<KeyValuePair<Faction, Faction>>();
                foreach (var entryA in _diplomaticStates)
                {
                    foreach (var entryB in entryA.Value)
                    {
                        var pair1 = new KeyValuePair<Faction, Faction>(entryA.Key, entryB.Key);
                        var pair2 = new KeyValuePair<Faction, Faction>(entryB.Key, entryA.Key);
                        if (!savedPairs.Contains(pair1) && !savedPairs.Contains(pair2))
                        {
                            diplomacyEntries.Add(new DiplomacyEntry(entryA.Key, entryB.Key, entryB.Value));
                            savedPairs.Add(pair1);
                        }
                    }
                }
            }

            Scribe_Collections.Look(ref diplomacyEntries, "diplomacyEntries", LookMode.Deep);

            if (Scribe.mode == LoadSaveMode.PostLoadInit)
            {
                _diplomaticStates = new Dictionary<Faction, Dictionary<Faction, DiplomaticState>>();
                if (diplomacyEntries != null)
                {
                    foreach (var entry in diplomacyEntries)
                    {
                        if (entry.factionA != null && entry.factionB != null)
                        {
                            SetDiplomaticState(entry.factionA, entry.factionB, entry.state, false); // No log spam on load
                        }
                    }
                }
            }

            // NEW: Trade Agreement Serialization
            List<TradeAgreement> tradeAgreementsList = new List<TradeAgreement>();
            if (Scribe.mode == LoadSaveMode.Saving)
            {
                var savedAgreements = new HashSet<TradeAgreement>();
                foreach (var entryA in _activeTradeAgreements.Values)
                {
                    foreach (var agreement in entryA.Values)
                    {
                        if (!savedAgreements.Contains(agreement))
                        {
                            tradeAgreementsList.Add(agreement);
                            savedAgreements.Add(agreement);
                        }
                    }
                }
            }
            Scribe_Collections.Look(ref tradeAgreementsList, "tradeAgreementsList", LookMode.Deep);
            if (Scribe.mode == LoadSaveMode.PostLoadInit)
            {
                _activeTradeAgreements = new Dictionary<Faction, Dictionary<Faction, TradeAgreement>>();
                if (tradeAgreementsList != null)
                {
                    foreach (var agreement in tradeAgreementsList)
                    {
                        if (agreement.FactionA == null || agreement.FactionB == null) continue; // Skip if reference lost.
                        SetTradeAgreement(agreement.FactionA, agreement.FactionB, agreement); // Re-add to dictionary.
                    }
                }
            }

            Scribe_Values.Look(ref _lastDiplomacyEvaluationTick, "lastDiplomacyEvaluationTick", -1);
        }

        // Helper struct for serialization.
        private class DiplomacyEntry : IExposable
        {
            public Faction factionA;
            public Faction factionB;
            public DiplomaticState state;

            // Parameterless constructor for Scribe
            public DiplomacyEntry() {}

            public DiplomacyEntry(Faction a, Faction b, DiplomaticState s)
            {
                factionA = a;
                factionB = b;
                state = s;
            }

            public void ExposeData()
            {
                Scribe_References.Look(ref factionA, "factionA");
                Scribe_References.Look(ref factionB, "factionB");
                Scribe_Values.Look(ref state, "state");
            }
        }


        // Public method to get the diplomatic state between two factions.
        public DiplomaticState GetDiplomaticState(Faction f1, Faction f2)
        {
            if (f1 == f2) return DiplomaticState.Neutral; 

            if (_diplomaticStates.TryGetValue(f1, out var innerDict) && innerDict.TryGetValue(f2, out var state))
            {
                // If our system says AtWar, that's the state.
                if(state == DiplomaticState.AtWar) return DiplomaticState.AtWar;
            }
            
            // Defer to RimWorld's hostility as the primary source for "Hostile" state
            if (f1.HostileTo(f2))
            {
                return DiplomaticState.Hostile;
            }

            // Return our stored state if not hostile in RimWorld's system.
            if (innerDict != null && innerDict.TryGetValue(f2, out var storedState))
            {
                return storedState;
            }

            return DiplomaticState.Neutral;
        }

        private void SetDiplomaticState(Faction f1, Faction f2, DiplomaticState state, bool logMessage = true)
        {
            if (f1 == f2) return;

            if (!_diplomaticStates.ContainsKey(f1))
            {
                _diplomaticStates[f1] = new Dictionary<Faction, DiplomaticState>();
            }
            _diplomaticStates[f1][f2] = state;

            if (!_diplomaticStates.ContainsKey(f2))
            {
                _diplomaticStates[f2] = new Dictionary<Faction, DiplomaticState>();
            }
            _diplomaticStates[f2][f1] = state;
            
            if (logMessage && (f1.IsPlayer || f2.IsPlayer))
            {
                Messages.Message($"Diplomatic state changed: {f1.Name} and {f2.Name} are now {state}.", MessageTypeDefOf.NeutralEvent);
            }
        }

        public override void WorldComponentTick()
        {
            base.WorldComponentTick();

            if (GenTicks.TicksGame >= _lastDiplomacyEvaluationTick + DIPLOMACY_EVALUATION_INTERVAL_TICKS)
            {
                EvaluateDiplomaticStates();
                
                // NEW: Tick active trade agreements.
                List<TradeAgreement> agreementsToRemove = new List<TradeAgreement>();
                var processedAgreements = new HashSet<TradeAgreement>();
                foreach (var entryA in _activeTradeAgreements.Values)
                {
                    foreach (var agreement in entryA.Values)
                    {
                        if (processedAgreements.Contains(agreement)) continue;

                        if (agreement.IsActive)
                        {
                            agreement.DoTradePayout();
                        }
                        else
                        {
                            agreementsToRemove.Add(agreement);
                        }
                        processedAgreements.Add(agreement);
                    }
                }
                foreach (var agreement in agreementsToRemove)
                {
                    RemoveTradeAgreement(agreement.FactionA, agreement.FactionB);
                }

                _lastDiplomacyEvaluationTick = GenTicks.TicksGame;
            }
        }

        private void EvaluateDiplomaticStates()
        {
            List<Faction> allFactions = Find.FactionManager.AllFactionsListForReading;

            for (int i = 0; i < allFactions.Count; i++)
            {
                Faction f1 = allFactions[i];
                if (f1.Hidden || !f1.def.humanlikeFaction || f1.def.permanentEnemy) continue;

                for (int j = i + 1; j < allFactions.Count; j++)
                {
                    Faction f2 = allFactions[j];
                    if (f2.Hidden || !f2.def.humanlikeFaction || f2.def.permanentEnemy) continue;

                    // Do not change state if currently "AtWar" unless explicitly making peace.
                    if (GetDiplomaticState(f1, f2) == DiplomaticState.AtWar) continue; 

                    int goodwill = f1.GoodwillWith(f2);
                    DiplomaticState newState = DiplomaticState.Neutral; // Default to neutral.

                    if (f1.HostileTo(f2)) // RimWorld's hostility takes precedence over Rival/Neutral.
                    {
                        newState = DiplomaticState.Hostile;
                    }
                    else if (goodwill >= 75)
                    {
                        newState = DiplomaticState.Allied;
                    }
                    else if (goodwill <= -50) // If goodwill drops low enough, become Rival.
                    {
                        newState = DiplomaticState.Rival;
                    }

                    if (GetDiplomaticState(f1, f2) != newState)
                    {
                        SetDiplomaticState(f1, f2, newState);
                    }
                }
            }
        }

        // NEW: Get active trade agreement.
        public TradeAgreement GetTradeAgreement(Faction f1, Faction f2)
        {
            if (_activeTradeAgreements.TryGetValue(f1, out var innerDict) && innerDict.TryGetValue(f2, out var agreement))
            {
                return agreement;
            }
            return null;
        }

        // NEW: Set active trade agreement.
        private void SetTradeAgreement(Faction f1, Faction f2, TradeAgreement agreement)
        {
            if (!_activeTradeAgreements.ContainsKey(f1)) _activeTradeAgreements[f1] = new Dictionary<Faction, TradeAgreement>();
            _activeTradeAgreements[f1][f2] = agreement;

            if (!_activeTradeAgreements.ContainsKey(f2)) _activeTradeAgreements[f2] = new Dictionary<Faction, TradeAgreement>();
            _activeTradeAgreements[f2][f1] = agreement; // Symmetric.
        }

        // NEW: Remove active trade agreement.
        public void RemoveTradeAgreement(Faction f1, Faction f2)
        {
            if (_activeTradeAgreements.TryGetValue(f1, out var innerDict))
            {
                innerDict.Remove(f2);
            }
            if (_activeTradeAgreements.TryGetValue(f2, out var innerDict2))
            {
                innerDict2.Remove(f1);
            }
            //Log.Message($"[NationalSystemMod.Diplomacy] Trade agreement between {f1.Name} and {f2.Name} has ended.");
            if (f1.IsPlayer || f2.IsPlayer)
            {
                Messages.Message($"Trade agreement between {f1.Name} and {f2.Name} has ended.", MessageTypeDefOf.NeutralEvent);
            }
        }

        public void TryInitiateTradeAgreement(Faction proposingFaction, Faction targetFaction)
        {
            if (proposingFaction == null || targetFaction == null || proposingFaction == targetFaction) return;
            if (GetTradeAgreement(proposingFaction, targetFaction) != null) 
            {
                if(proposingFaction.IsPlayer) Messages.Message($"You already have an active trade agreement with {targetFaction.Name}.", MessageTypeDefOf.RejectInput, false);
                return;
            }

            int goodwill = proposingFaction.GoodwillWith(targetFaction);
            if (goodwill < 10) 
            {
                if (proposingFaction.IsPlayer) Messages.Message($"Not enough goodwill with {targetFaction.Name} to propose a trade agreement.", MessageTypeDefOf.RejectInput, false);
                return;
            }

            // AI acceptance logic
            float acceptanceChance = 0.5f + (goodwill / 200f); 
            NationalData targetData = targetFaction.GetNationalData();
            if (targetData != null && targetData.CurrentPolicy != null)
            {
                acceptanceChance *= targetData.CurrentPolicy.tradeInterestFactor;
            }

            if (Rand.Value < acceptanceChance)
            {
                // Agreement accepted
                float baseValue = 500f; 
                int durationDays = 60; 
                TradeAgreement newAgreement = new TradeAgreement(proposingFaction, targetFaction, durationDays, baseValue);
                SetTradeAgreement(proposingFaction, targetFaction, newAgreement);

                if (proposingFaction.IsPlayer || targetFaction.IsPlayer)
                {
                    Messages.Message($"{targetFaction.Name} has accepted the trade agreement.", MessageTypeDefOf.PositiveEvent, false);
                }
            }
            else
            {
                // Agreement rejected
                if (proposingFaction.IsPlayer)
                {
                    Messages.Message($"{targetFaction.Name} has rejected the trade agreement proposal.", MessageTypeDefOf.NegativeEvent, false);
                }
                proposingFaction.TryAffectGoodwillWith(targetFaction, -5, true, true, null, null);
            }
        }

        // --- NEW PUBLIC METHODS FOR WAR AND PEACE ---

        // Player or AI declares war.
        public void DeclareWar(Faction declaringFaction, Faction targetFaction)
        {
            if (declaringFaction == null || targetFaction == null || declaringFaction == targetFaction) return;
            if (GetDiplomaticState(declaringFaction, targetFaction) == DiplomaticState.AtWar) return;

            // Immediately set RimWorld's hostility and our custom state.
            declaringFaction.SetRelationDirect(targetFaction, FactionRelationKind.Hostile, false, "NationalSystemMod_WarDeclared");
            SetDiplomaticState(declaringFaction, targetFaction, DiplomaticState.AtWar);

            // Break any active trade agreements.
            if (GetTradeAgreement(declaringFaction, targetFaction) != null)
            {
                RemoveTradeAgreement(declaringFaction, targetFaction);
            }

            Messages.Message($"{declaringFaction.Name} has declared war on {targetFaction.Name}!",
                             (declaringFaction.IsPlayer || targetFaction.IsPlayer) ? MessageTypeDefOf.NegativeEvent : MessageTypeDefOf.NeutralEvent, false);

            // Optional: Trigger an immediate raid if declaring faction is player and target is AI, or vice versa.
            if ((declaringFaction.IsPlayer || targetFaction.IsPlayer) && Rand.Value < 0.3f) // 30% chance of immediate raid.
            {
                Faction aggressorForRaid = declaringFaction.IsPlayer ? targetFaction : declaringFaction;
                InfluenceManager.TryTriggerContestEvent(aggressorForRaid, declaringFaction.IsPlayer ? declaringFaction : targetFaction, -1, false); // -1 for no specific tile, just a general raid.
            }
        }

        // Player or AI attempts to make peace.
        public void ProposePeace(Faction proposingFaction, Faction targetFaction)
        {
            if (proposingFaction == null || targetFaction == null || proposingFaction == targetFaction) return;
            if (GetDiplomaticState(proposingFaction, targetFaction) != DiplomaticState.AtWar)
            {
                if (proposingFaction.IsPlayer || targetFaction.IsPlayer) Messages.Message("Not currently at war.", MessageTypeDefOf.RejectInput, false);
                return;
            }

            // Conditions for peace:
            int goodwill = proposingFaction.GoodwillWith(targetFaction);
            float peaceChance = 0.3f; // Base chance.
            if (goodwill >= 0) peaceChance += 0.4f; // Much easier if goodwill is neutral/positive.
            if (goodwill > -20) peaceChance += 0.2f;
            
            // Influence of military power difference (weaker side more likely to accept peace).
            NationalData proposingData = proposingFaction.GetNationalData();
            NationalData targetData = targetFaction.GetNationalData();
            if (proposingData != null && targetData != null)
            {
                if (proposingData.MilitaryPower < targetData.MilitaryPower * 0.8f) peaceChance += 0.1f; // Proposer is weaker.
                if (targetData.MilitaryPower < proposingData.MilitaryPower * 0.8f) peaceChance += 0.1f; // Target is weaker.
            }
            peaceChance = Mathf.Clamp01(peaceChance);

            if (Rand.Value < peaceChance)
            {
                // Peace successful
                proposingFaction.SetRelationDirect(targetFaction, FactionRelationKind.Neutral, false, "NationalSystemMod_PeaceTreaty");
                SetDiplomaticState(proposingFaction, targetFaction, DiplomaticState.Neutral);

                proposingFaction.TryAffectGoodwillWith(targetFaction, 20, true, true, null, null);
                
                Messages.Message($"Peace has been negotiated between {proposingFaction.Name} and {targetFaction.Name}!", MessageTypeDefOf.PositiveEvent, false);
            }
            else
            {
                Messages.Message($"Peace proposal rejected by {targetFaction.Name}.", MessageTypeDefOf.NegativeEvent, false);
                proposingFaction.TryAffectGoodwillWith(targetFaction, -10, true, true, null, null);
            }
        }

        // NEW: Handles AI decision to declare war (simple for now).
        public void AIDeclareWarLogic()
        {
            foreach (Faction faction in Find.FactionManager.AllFactionsListForReading)
            {
                NationalData nationalData = faction.GetNationalData();
                if (nationalData == null || !nationalData.IsValid() || faction.IsPlayer || nationalData.CurrentPolicy == null) continue;

                // Simple AI: If we are Rival or Hostile with a weaker faction, consider declaring war.
                foreach (Faction otherFaction in Find.FactionManager.AllFactionsListForReading)
                {
                    if (otherFaction == faction || otherFaction.IsPlayer || !otherFaction.def.humanlikeFaction || otherFaction.def.permanentEnemy) continue;

                    DiplomaticState state = GetDiplomaticState(faction, otherFaction);
                    NationalData otherNationalData = otherFaction.GetNationalData();
                    if (otherNationalData == null) continue;

                    if (state == DiplomaticState.Rival || state == DiplomaticState.Hostile)
                    {
                        // Compare military power, factor in policy aggression.
                        float powerRatio = nationalData.MilitaryPower / otherNationalData.MilitaryPower;
                        float aggressionThreshold = 1.2f / nationalData.CurrentPolicy.militaryAggressionFactor;

                        if (powerRatio > aggressionThreshold && Rand.Value < 0.1f * nationalData.CurrentPolicy.militaryAggressionFactor)
                        {
                            DeclareWar(faction, otherFaction);
                            return; 
                        }
                    }
                }
            }
        }
    }
} 