using Verse;
using Rim<PERSON>orld;
using RimWorld.Planet;
using System.Collections.Generic;
using System.Linq;
using AIGen.NationalSystemMod.Nation;
using AIGen.NationalSystemMod.Defs;
using AIGen.NationalSystemMod.Common;
using AIGen.NationalSystemMod.Economy;
using UnityEngine;

namespace AIGen.NationalSystemMod.World
{
    public enum DiplomaticState
    {
        Neutral,
        Allied,
        Rival,
        Hostile,
        AtWar
    }

    public class FactionDiplomacyTracker : WorldComponent
    {
        private Dictionary<Faction, Dictionary<Faction, DiplomaticState>> _diplomaticStates = new Dictionary<Faction, Dictionary<Faction, DiplomaticState>>();
        private List<TradeOffer> _pendingTradeOffers = new List<TradeOffer>();
        private Dictionary<Faction, List<WorldArmy>> _activeArmiesByFaction = new Dictionary<Faction, List<WorldArmy>>();

        private const int DIPLOMACY_EVALUATION_INTERVAL_TICKS = GenDate.TicksPerDay * 2;
        private int _lastDiplomacyEvaluationTick = -1;
        
        // This constructor is required by RimWorld for all WorldComponents.
        public FactionDiplomacyTracker(RimWorld.Planet.World world) : base(world) { }

        public override void ExposeData()
        {
            base.ExposeData();
            
            // Custom serialization for the nested dictionary
            List<DiplomacyEntry> diplomacyEntries = new List<DiplomacyEntry>();
            if (Scribe.mode == LoadSaveMode.Saving)
            {
                var savedPairs = new HashSet<FactionPair>();
                if (_diplomaticStates != null)
                {
                    foreach (var entryA in _diplomaticStates)
                    {
                        foreach (var entryB in entryA.Value)
                        {
                            var pair = new FactionPair(entryA.Key, entryB.Key);
                            if (!savedPairs.Contains(pair))
                            {
                                diplomacyEntries.Add(new DiplomacyEntry(entryA.Key, entryB.Key, entryB.Value));
                                savedPairs.Add(pair);
                            }
                        }
                    }
                }
            }
            Scribe_Collections.Look(ref diplomacyEntries, "diplomacyEntries", LookMode.Deep);
            if (Scribe.mode == LoadSaveMode.PostLoadInit)
            {
                _diplomaticStates = new Dictionary<Faction, Dictionary<Faction, DiplomaticState>>();
                if (diplomacyEntries != null)
                {
                    foreach (var entry in diplomacyEntries)
                    {
                        if (entry.factionA != null && entry.factionB != null)
                        {
                            SetDiplomaticState(entry.factionA, entry.factionB, entry.state);
                        }
                    }
                }
            }

            Scribe_Collections.Look(ref _pendingTradeOffers, "pendingTradeOffers", LookMode.Deep);
            Scribe_Values.Look(ref _lastDiplomacyEvaluationTick, "lastDiplomacyEvaluationTick", -1);
            
            if (Scribe.mode == LoadSaveMode.PostLoadInit)
            {
                if (_pendingTradeOffers == null) _pendingTradeOffers = new List<TradeOffer>();
                
                _activeArmiesByFaction = new Dictionary<Faction, List<WorldArmy>>();
                foreach (var army in Find.WorldObjects.AllWorldObjects.OfType<WorldArmy>())
                {
                    if (army != null && army.Faction != null && !army.Destroyed)
                    {
                       AddArmy(army);
                    }
                }
            }
        }

        private struct FactionPair : System.IEquatable<FactionPair>
        {
            public readonly Faction Faction1;
            public readonly Faction Faction2;

            public FactionPair(Faction f1, Faction f2)
            {
                if (f1.loadID < f2.loadID) { Faction1 = f1; Faction2 = f2; }
                else { Faction1 = f2; Faction2 = f1; }
            }

            public bool Equals(FactionPair other) => Faction1 == other.Faction1 && Faction2 == other.Faction2;
            public override int GetHashCode() => (Faction1.loadID, Faction2.loadID).GetHashCode();
        }

        private class DiplomacyEntry : IExposable
        {
            public Faction factionA;
            public Faction factionB;
            public DiplomaticState state;
            public DiplomacyEntry() {}
            public DiplomacyEntry(Faction a, Faction b, DiplomaticState s) { factionA = a; factionB = b; state = s; }
            public void ExposeData()
            {
                Scribe_References.Look(ref factionA, "factionA");
                Scribe_References.Look(ref factionB, "factionB");
                Scribe_Values.Look(ref state, "state");
            }
        }

        public DiplomaticState GetDiplomaticState(Faction f1, Faction f2)
        {
            if (f1 == null || f2 == null || f1 == f2) return DiplomaticState.Neutral;
            if (_diplomaticStates.TryGetValue(f1, out var innerDict) && innerDict.TryGetValue(f2, out var state))
            {
                return state;
            }
            if (f1.HostileTo(f2)) return DiplomaticState.Hostile;
            return DiplomaticState.Neutral;
        }

        public Dictionary<Faction, DiplomaticState> GetAllRelationsForFaction(Faction f)
        {
            if (f != null && _diplomaticStates.TryGetValue(f, out var relations))
            {
                return new Dictionary<Faction, DiplomaticState>(relations);
            }
            return new Dictionary<Faction, DiplomaticState>();
        }

        public void SetDiplomaticState(Faction f1, Faction f2, DiplomaticState state)
        {
            if (f1 == null || f2 == null || f1 == f2) return;
            if (!_diplomaticStates.ContainsKey(f1)) _diplomaticStates[f1] = new Dictionary<Faction, DiplomaticState>();
            _diplomaticStates[f1][f2] = state;
            if (!_diplomaticStates.ContainsKey(f2)) _diplomaticStates[f2] = new Dictionary<Faction, DiplomaticState>();
            _diplomaticStates[f2][f1] = state;
        }
        
        public void ChangeGoodwill(Faction faction1, Faction faction2, int change, bool canSendMessage = true)
        {
            if (faction1 == null || faction2 == null || faction1 == faction2) return;
            
            int currentGoodwill = faction1.GoodwillWith(faction2);
            int newGoodwill = Mathf.Clamp(currentGoodwill + change, -100, 100);
            
            faction1.SetRelationDirect(faction2, (FactionRelationKind)Mathf.Clamp((int)faction1.RelationKindWith(faction2), 0, 2), newGoodwill);

            if (canSendMessage)
            {
                string reason = change > 0 ? "GoodwillIncreased" : "GoodwillDecreased";
                Find.LetterStack.ReceiveLetter(
                    "GoodwillChangeLetter".Translate(),
                    $"GoodwillChangeDesc".Translate(faction1.Name, faction2.Name, change, newGoodwill),
                    LetterDefOf.NeutralEvent,
                    null,
                    faction1
                );
            }
        }

        public void ApplyEventGoodwillChanges(WorldEventDef eventDef, Faction primaryFaction)
        {
            if (eventDef == null || primaryFaction == null || eventDef.goodwillChangeFactor == 0) return;

            foreach (Faction otherFaction in Find.FactionManager.AllFactionsVisible)
            {
                if (otherFaction == primaryFaction || !otherFaction.def.canBePlayerMechanicsFaction) continue;
                
                // Example logic: Change depends on ideology, distance, etc.
                float modifier = 1f; 
                int goodwillChange = Mathf.RoundToInt(eventDef.goodwillChangeFactor * modifier);
                
                ChangeGoodwill(primaryFaction, otherFaction, goodwillChange);
            }
        }

        public void AddTradeOffer(TradeOffer offer)
        {
            _pendingTradeOffers.Add(offer);
            if (offer.TargetFaction.IsPlayer)
            {
                Messages.Message($"New trade offer from {offer.ProposingFaction.Name}!", MessageTypeDefOf.NeutralEvent);
            }
        }

        public List<TradeOffer> GetTradeOffersForFaction(Faction faction)
        {
            _pendingTradeOffers.RemoveAll(o => o == null || o.ExpiryTick < GenTicks.TicksGame);
            return _pendingTradeOffers.Where(o => o.TargetFaction == faction).ToList();
        }

        public bool AcceptTradeOffer(TradeOffer offer)
        {
            if (_pendingTradeOffers.Contains(offer) && offer.TryExecuteTrade())
            {
                _pendingTradeOffers.Remove(offer);
                return true;
            }
            return false;
        }

        public void RejectTradeOffer(TradeOffer offer)
        {
            if (_pendingTradeOffers.Contains(offer))
            {
                _pendingTradeOffers.Remove(offer);
            }
        }
        
        public void AddArmy(WorldArmy army)
        {
            if (army?.Faction == null) return;
            if (!_activeArmiesByFaction.ContainsKey(army.Faction))
            {
                _activeArmiesByFaction[army.Faction] = new List<WorldArmy>();
            }
            if (!_activeArmiesByFaction[army.Faction].Contains(army))
            {
                _activeArmiesByFaction[army.Faction].Add(army);
            }
        }

        public void RemoveArmy(WorldArmy army)
        {
            if (army?.Faction == null || !_activeArmiesByFaction.ContainsKey(army.Faction)) return;
            _activeArmiesByFaction[army.Faction].Remove(army);
        }

        public List<WorldArmy> GetArmiesForFaction(Faction faction)
        {
            if (faction != null && _activeArmiesByFaction.TryGetValue(faction, out var armies))
            {
                armies.RemoveAll(a => a == null || a.Destroyed);
                return armies.ToList();
            }
            return new List<WorldArmy>();
        }

        public override void WorldComponentTick()
        {
            base.WorldComponentTick();
            if (GenTicks.TicksGame > _lastDiplomacyEvaluationTick + DIPLOMACY_EVALUATION_INTERVAL_TICKS)
            {
                EvaluateDiplomaticRelations();
                _lastDiplomacyEvaluationTick = GenTicks.TicksGame;
            }
        }

        private void EvaluateDiplomaticRelations()
        {
            foreach (var faction in Find.FactionManager.AllFactionsVisible)
            {
                if (faction.IsPlayer || !faction.def.canBePlayerMechanicsFaction) continue;
                
                AILogicForFaction(faction);
            }
        }
        
        private void AILogicForFaction(Faction faction)
        {
            var nationalData = faction.GetNationalData();
            if (nationalData == null || !nationalData.IsValid()) return;

            // NEW: AI Army Management
            AIManageArmies(faction, nationalData);

            // Existing strategic goal logic
            if (nationalData.CurrentStrategicGoal != null)
            {
                switch (nationalData.CurrentStrategicGoal.GetGoalType())
                {
                    case StrategicGoalType.MilitaryExpansion:
                        ConsiderDeclaringWar(faction, nationalData);
                        break;
                    case StrategicGoalType.DiplomaticSupremacy:
                        ConsiderProposingAlliance(faction, nationalData);
                        break;
                    case StrategicGoalType.EconomicGrowth:
                        ConsiderMakingTradeOffer(faction, nationalData);
                        break;
                }
            }
        }

        private void ConsiderDeclaringWar(Faction faction, NationalData nationalData)
        {
            var potentialTargets = Find.FactionManager.AllFactionsVisible
                .Where(f => f != faction && !f.HostileTo(faction) && f.def.canBePlayerMechanicsFaction)
                .ToList();

            if (!potentialTargets.Any()) return;

            var target = potentialTargets.RandomElement(); 
            
            float ourStrength = GetFactionMilitaryStrength(faction);
            float theirStrength = GetFactionMilitaryStrength(target);

            if (ourStrength > theirStrength * 1.5f)
            {
                DeclareWar(faction, target, "CasusBelli_Expansion");
            }
        }

        private void ConsiderProposingAlliance(Faction faction, NationalData nationalData)
        {
            // Logic to propose alliance
        }

        private void ConsiderMakingTradeOffer(Faction faction, NationalData nationalData)
        {
            // Logic to make trade offers
        }
        
        public float GetFactionMilitaryStrength(Faction faction)
        {
            var armies = GetArmiesForFaction(faction);
            float totalStrength = armies.Sum(a => a.MilitaryStrength);
            
            var nationalData = faction.GetNationalData();
            if (nationalData != null)
            {
                totalStrength += nationalData.MilitaryPower; // Undeployed military power
            }
            
            return totalStrength;
        }

        public void DeclareWar(Faction declaringFaction, Faction targetFaction, string casusBelliKey)
        {
            if (GetDiplomaticState(declaringFaction, targetFaction) == DiplomaticState.AtWar) return;

            SetDiplomaticState(declaringFaction, targetFaction, DiplomaticState.AtWar);
            ChangeGoodwill(declaringFaction, targetFaction, -80);
            
            Find.LetterStack.ReceiveLetter(
                "WarDeclaredLetter".Translate(),
                "WarDeclaredDesc".Translate(declaringFaction.Name, targetFaction.Name, casusBelliKey.Translate()),
                LetterDefOf.NegativeEvent
            );
        }

        // NEW: AI logic for creating and moving armies.
        private void AIManageArmies(Faction faction, NationalData nationalData)
        {
            if (faction.IsPlayer) return;

            // Get existing armies for this faction.
            List<WorldArmy> armies = GetArmiesForFaction(faction);
            float totalArmyStrength = armies.Sum(a => a.MilitaryStrength);

            // Decide whether to build new armies.
            // Example: If military power is high, but army strength is low, or if at war.
            if (nationalData.MilitaryPower > 100f && totalArmyStrength < nationalData.MilitaryPower * 0.5f && Rand.Value < 0.1f) // 10% chance to build
            {
                // Find a suitable tile for a new army (e.g., owned tile with high influence).
                int spawnTile = CoreLogic.WorldInfluenceMap.InfluenceDataByTile
                    .Where(kvp => kvp.Value.faction == faction)
                    .OrderByDescending(kvp => kvp.Value.influence)
                    .Select(kvp => kvp.Key)
                    .FirstOrDefault();

                if (spawnTile != 0) // Tile 0 is usually invalid or placeholder.
                {
                    WorldArmy newArmy = (WorldArmy)WorldObjectMaker.MakeWorldObject(NationalSystemDefOf.NationalSystem_WorldArmy);
                    newArmy.Tile = spawnTile;
                    newArmy.SetFaction(faction);
                    newArmy.MilitaryStrength = Rand.Range(30f, 60f); // Initial strength.
                    newArmy.OriginTile = spawnTile;
                    Find.WorldObjects.Add(newArmy);
                    AddArmy(newArmy); // Track in our system
                    nationalData.MilitaryPower -= newArmy.MilitaryStrength; // Deduct from national military power as it's now 'deployed'.
                    Log.Message($"[NationalSystemMod.AI] {faction.Name} created a new army with strength {newArmy.MilitaryStrength:F0} at tile {spawnTile}.");
                }
            }

            // Decide army actions (movement, targeting)
            foreach (WorldArmy army in armies)
            {
                // If army has no target, find one.
                if (army.TargetTile == -1)
                {
                    // Scenario 1: Suppress rebel army in own territory.
                    WorldArmy rebelArmy = Find.WorldObjects.AllWorldObjects.OfType<WorldArmy>()
                        .FirstOrDefault(a => a.Faction != faction && GetDiplomaticState(faction, a.Faction) == DiplomaticState.AtWar && 
                                            CoreLogic.WorldInfluenceMap.GetInfluenceData(a.Tile)?.faction == faction);

                    if (rebelArmy != null)
                    {
                        army.SetNewTarget(rebelArmy.Tile);
                        Log.Message($"[NationalSystemMod.AI] {faction.Name}'s army moving to suppress rebellion at tile {rebelArmy.Tile}.");
                        continue;
                    }

                    // Scenario 2: Attack a rival/hostile faction.
                    Faction targetFaction = Find.FactionManager.AllFactionsListForReading
                        .Where(f => f != faction && !f.IsPlayer && !f.IsHidden && 
                                   (GetDiplomaticState(faction, f) == DiplomaticState.AtWar || GetDiplomaticState(faction, f) == DiplomaticState.Hostile))
                        .RandomElementWithFallback();
                    
                    if (targetFaction != null)
                    {
                        // Find a target tile in enemy territory (e.g., enemy settlement or influential tile).
                        int targetTile = CoreLogic.WorldInfluenceMap.InfluenceDataByTile
                            .Where(kvp => kvp.Value.faction == targetFaction)
                            .OrderByDescending(kvp => kvp.Value.influence)
                            .Select(kvp => kvp.Key)
                            .FirstOrDefault();
                        
                        if (targetTile != 0)
                        {
                            army.SetNewTarget(targetTile);
                            Log.Message($"[NationalSystemMod.AI] {faction.Name}'s army moving to attack {targetFaction.Name} at tile {targetTile}.");
                            continue;
                        }
                    }

                    // Scenario 3: Garrison at a high-value owned tile (e.g., border, high resource).
                    int garrisonTile = CoreLogic.WorldInfluenceMap.InfluenceDataByTile
                        .Where(kvp => kvp.Value.faction == faction && kvp.Value.influence > 50)
                        .Select(kvp => kvp.Key)
                        .RandomElementWithFallback();
                    
                    if (garrisonTile != 0)
                    {
                        army.SetNewTarget(garrisonTile);
                        Log.Message($"[NationalSystemMod.AI] {faction.Name}'s army moving to garrison at tile {garrisonTile}.");
                    }
                }
            }
        }
    }
}