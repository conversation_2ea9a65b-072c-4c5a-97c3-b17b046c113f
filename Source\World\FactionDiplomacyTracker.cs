using Verse;
using Rim<PERSON>orld;
using RimWorld.Planet;
using UnityEngine; // For Mathf
using System.Collections.Generic;
using System.Linq;
using AIGen.NationalSystemMod.Common;
using AIGen.NationalSystemMod.Nation;
using AIGen.NationalSystemMod.Utils;
using AIGen.NationalSystemMod.Economy;
using AIGen.NationalSystemMod.StrategicAI;
using AIGen.NationalSystemMod.Defs;

namespace AIGen.NationalSystemMod.World
{
    public enum DiplomaticState
    {
        Neutral,
        Allied,
        Rival,
        Hostile,
        AtWar
    }

    public class FactionDiplomacyTracker : WorldComponent
    {
        private Dictionary<Faction, Dictionary<Faction, DiplomaticState>> _diplomaticStates;
        private List<TradeOffer> _pendingTradeOffers;
        private Dictionary<Faction, List<WorldArmy>> _activeArmiesByFaction;

        private const int DIPLOMACY_EVALUATION_INTERVAL_TICKS = GenDate.TicksPerDay * 2;
        private int _lastDiplomacyEvaluationTick = -1;

        public FactionDiplomacyTracker(RimWorld.Planet.World world) : base(world)
        {
            _diplomaticStates = new Dictionary<Faction, Dictionary<Faction, DiplomaticState>>();
            _pendingTradeOffers = new List<TradeOffer>();
            _activeArmiesByFaction = new Dictionary<Faction, List<WorldArmy>>();
        }

        public override void ExposeData()
        {
            base.ExposeData();
            
            List<DiplomacyEntry> diplomacyEntries = new List<DiplomacyEntry>();
            if (Scribe.mode == LoadSaveMode.Saving)
            {
                var savedPairs = new HashSet<KeyValuePair<Faction, Faction>>();
                if (_diplomaticStates != null)
                {
                    foreach (var entryA in _diplomaticStates)
                    {
                        foreach (var entryB in entryA.Value)
                        {
                            var pair1 = new KeyValuePair<Faction, Faction>(entryA.Key, entryB.Key);
                            var pair2 = new KeyValuePair<Faction, Faction>(entryB.Key, entryA.Key);
                            if (!savedPairs.Contains(pair1) && !savedPairs.Contains(pair2))
                            {
                                diplomacyEntries.Add(new DiplomacyEntry(entryA.Key, entryB.Key, entryB.Value));
                                savedPairs.Add(pair1);
                            }
                        }
                    }
                }
            }
            Scribe_Collections.Look(ref diplomacyEntries, "diplomacyEntries", LookMode.Deep);
            if (Scribe.mode == LoadSaveMode.PostLoadInit)
            {
                _diplomaticStates = new Dictionary<Faction, Dictionary<Faction, DiplomaticState>>();
                if (diplomacyEntries != null)
                {
                    foreach (var entry in diplomacyEntries)
                    {
                        if (entry.factionA != null && entry.factionB != null)
                        {
                            SetDiplomaticState(entry.factionA, entry.factionB, entry.state);
                        }
                    }
                }
            }

            Scribe_Collections.Look(ref _pendingTradeOffers, "pendingTradeOffers", LookMode.Deep);
            if (Scribe.mode == LoadSaveMode.PostLoadInit && _pendingTradeOffers == null)
            {
                _pendingTradeOffers = new List<TradeOffer>();
            }

            if (Scribe.mode == LoadSaveMode.PostLoadInit)
            {
                _activeArmiesByFaction = new Dictionary<Faction, List<WorldArmy>>();
                foreach (WorldArmy army in Find.WorldObjects.AllWorldObjects.OfType<WorldArmy>())
                {
                    if (army.Faction != null && !army.Destroyed)
                    {
                       AddArmy(army);
                    }
                }
            }
            
            Scribe_Values.Look(ref _lastDiplomacyEvaluationTick, "lastDiplomacyEvaluationTick", -1);
        }

        private class DiplomacyEntry : IExposable
        {
            public Faction factionA;
            public Faction factionB;
            public DiplomaticState state;

            public DiplomacyEntry() {}

            public DiplomacyEntry(Faction a, Faction b, DiplomaticState s)
            {
                factionA = a;
                factionB = b;
                state = s;
            }

            public void ExposeData()
            {
                Scribe_References.Look(ref factionA, "factionA");
                Scribe_References.Look(ref factionB, "factionB");
                Scribe_Values.Look(ref state, "state");
            }
        }

        public DiplomaticState GetDiplomaticState(Faction f1, Faction f2)
        {
            if (f1 == null || f2 == null || f1 == f2) return DiplomaticState.Neutral;
            if (_diplomaticStates.TryGetValue(f1, out var innerDict) && innerDict.TryGetValue(f2, out var state))
            {
                return state;
            }
            if (f1.HostileTo(f2)) return DiplomaticState.Hostile;
            return DiplomaticState.Neutral;
        }

        private void SetDiplomaticState(Faction f1, Faction f2, DiplomaticState state)
        {
            if (f1 == null || f2 == null || f1 == f2) return;
            if (!_diplomaticStates.ContainsKey(f1)) _diplomaticStates[f1] = new Dictionary<Faction, DiplomaticState>();
            _diplomaticStates[f1][f2] = state;
            if (!_diplomaticStates.ContainsKey(f2)) _diplomaticStates[f2] = new Dictionary<Faction, DiplomaticState>();
            _diplomaticStates[f2][f1] = state;
        }

        public void AddTradeOffer(TradeOffer offer)
        {
            _pendingTradeOffers.Add(offer);
            if (offer.TargetFaction.IsPlayer)
            {
                Messages.Message($"New trade offer from {offer.ProposingFaction.Name}!", MessageTypeDefOf.NeutralEvent);
            }
        }

        public List<TradeOffer> GetTradeOffersForFaction(Faction faction)
        {
            _pendingTradeOffers.RemoveAll(o => o == null);
            return _pendingTradeOffers.Where(o => o.TargetFaction == faction && GenTicks.TicksGame < o.ExpiryTick).ToList();
        }

        public bool AcceptTradeOffer(TradeOffer offer)
        {
            if (_pendingTradeOffers.Contains(offer) && offer.TryExecuteTrade())
            {
                _pendingTradeOffers.Remove(offer);
                return true;
            }
            return false;
        }

        public void RejectTradeOffer(TradeOffer offer)
        {
            if (_pendingTradeOffers.Contains(offer))
            {
                _pendingTradeOffers.Remove(offer);
            }
        }

        public TradeOffer GetTradeAgreement(Faction f1, Faction f2)
        {
            // Return the first active trade offer between these factions
            return _pendingTradeOffers.FirstOrDefault(o =>
                (o.ProposingFaction == f1 && o.TargetFaction == f2) ||
                (o.ProposingFaction == f2 && o.TargetFaction == f1));
        }

        public void TryInitiateTradeAgreement(Faction proposer, Faction target)
        {
            // Simple trade agreement initiation
            var offered = new Dictionary<NationalResourceType, float>();
            var requested = new Dictionary<NationalResourceType, float>();

            // Add some basic trade items
            offered[NationalResourceType.Silver] = 100f;
            requested[NationalResourceType.Food] = 50f;

            AddTradeOffer(new TradeOffer(proposer, target, offered, requested, 10));
        }

        public List<WorldArmy> GetArmiesForFaction(Faction faction)
        {
            if (faction != null && _activeArmiesByFaction.TryGetValue(faction, out var armies))
            {
                armies.RemoveAll(a => a == null || a.Destroyed);
                return armies.ToList();
            }
            return new List<WorldArmy>();
        }

        public void AddArmy(WorldArmy army)
        {
            if (army?.Faction == null) return;
            if (!_activeArmiesByFaction.ContainsKey(army.Faction))
            {
                _activeArmiesByFaction[army.Faction] = new List<WorldArmy>();
            }
            if (!_activeArmiesByFaction[army.Faction].Contains(army))
            {
                _activeArmiesByFaction[army.Faction].Add(army);
            }
        }

        public void RemoveArmy(WorldArmy army)
        {
            if (army?.Faction != null && _activeArmiesByFaction.TryGetValue(army.Faction, out var armies))
            {
                armies.Remove(army);
            }
        }

        public override void WorldComponentTick()
        {
            base.WorldComponentTick();
            if (GenTicks.TicksGame >= _lastDiplomacyEvaluationTick + DIPLOMACY_EVALUATION_INTERVAL_TICKS)
            {
                EvaluateDiplomaticStates();
                _lastDiplomacyEvaluationTick = GenTicks.TicksGame;
            }
            _pendingTradeOffers.RemoveAll(offer => offer == null || GenTicks.TicksGame >= offer.ExpiryTick);
        }

        private void EvaluateDiplomaticStates() 
        {
             // Future logic here
        }

        public void DeclareWar(Faction declaringFaction, Faction targetFaction)
        {
            if (declaringFaction == null || targetFaction == null || declaringFaction == targetFaction || GetDiplomaticState(declaringFaction, targetFaction) == DiplomaticState.AtWar) return;

            declaringFaction.SetRelationDirect(targetFaction, FactionRelationKind.Hostile, false, "NationalSystemMod_WarDeclared");
            SetDiplomaticState(declaringFaction, targetFaction, DiplomaticState.AtWar);

            _pendingTradeOffers.RemoveAll(o => (o.ProposingFaction == declaringFaction && o.TargetFaction == targetFaction) || (o.ProposingFaction == targetFaction && o.TargetFaction == declaringFaction));
            Messages.Message($"{declaringFaction.Name} has declared war on {targetFaction.Name}!", (declaringFaction.IsPlayer || targetFaction.IsPlayer) ? MessageTypeDefOf.NegativeEvent : MessageTypeDefOf.NeutralEvent);

            NationalData declaringData = declaringFaction.GetNationalData();
            if (declaringData != null && declaringData.MilitaryPower > 50f)
            {
                Settlement originSettlement = Find.WorldObjects.Settlements.Where(s => s.Faction == declaringFaction).RandomElementWithFallback(null);
                if (originSettlement != null)
                {
                    WorldArmy newArmy = CoreLogic.CreateFactionArmy(declaringFaction, originSettlement.Tile, declaringData.MilitaryPower * 0.5f);
                    Settlement targetSettlement = Find.WorldObjects.Settlements.Where(s => s.Faction == targetFaction).RandomElementWithFallback(null);
                    if (targetSettlement != null)
                    {
                        newArmy.SetNewTarget(targetSettlement.Tile);
                        Messages.Message($"A new army ({newArmy.Label}) has been raised by {declaringFaction.Name} and is marching towards {targetSettlement.Label}!", MessageTypeDefOf.NeutralEvent);
                    }
                }
            }
        }

        public void ProposePeace(Faction proposingFaction, Faction targetFaction) 
        {
            // Future logic here
        }

        public void AIDeclareWarLogic() 
        {
             // Future logic here
        }

        public void AIGenerateTradeOffers()
        {
            foreach (Faction proposer in Find.FactionManager.AllFactions.Where(f => f.def.humanlikeFaction && !f.IsPlayer && !f.Hidden))
            {
                NationalData proposerData = proposer.GetNationalData();
                if (proposerData?.CurrentPolicy == null) continue;

                if (Rand.Value < 0.2f * proposerData.CurrentPolicy.tradeInterestFactor)
                {
                    Faction target = Find.FactionManager.AllFactions.Where(f => f != proposer && f.def.humanlikeFaction && !f.Hidden && GetDiplomaticState(proposer, f) != DiplomaticState.AtWar).RandomElementWithFallback(null);
                    if (target == null) continue;
                    
                    var offered = new Dictionary<NationalResourceType, float>();
                    var requested = new Dictionary<NationalResourceType, float>();
                    
                    foreach (NationalResourceType type in System.Enum.GetValues(typeof(NationalResourceType)))
                    {
                        if (type == NationalResourceType.None || type == NationalResourceType.Silver) continue;
                        var resources = proposerData.Resources;
                        if (resources.GetResourceAmount(type) > 200f && Rand.Value < 0.5f) offered[type] = resources.GetResourceAmount(type) * Rand.Range(0.1f, 0.3f);
                        if (resources.GetResourceAmount(type) < 50f && Rand.Value < 0.5f)
                        {
                            var targetData = target.GetNationalData();
                            if(targetData == null) continue;
                            float targetSurplus = targetData.Resources.GetResourceAmount(type) - 50f;
                            if (targetSurplus > 0) requested[type] = Mathf.Min(targetSurplus * Rand.Range(0.1f, 0.3f), 50f);
                        }
                    }
                    
                    if (!offered.Any() && !requested.Any()) continue;

                    float netValue = offered.Sum(o => o.Value * TradeOffer.GetResourceMarketValue(o.Key)) - requested.Sum(r => r.Value * TradeOffer.GetResourceMarketValue(r.Key));
                    if (netValue > 0) requested[NationalResourceType.Silver] = netValue;
                    else if (netValue < 0) offered[NationalResourceType.Silver] = -netValue;

                    if (offered.Any() || requested.Any()) AddTradeOffer(new TradeOffer(proposer, target, offered, requested, Rand.Range(5, 15)));
                }
            }
        }

        public void AIEvaluateTradeOffers()
        {
            foreach (TradeOffer offer in _pendingTradeOffers.ToList())
            {
                if (offer.TargetFaction.IsPlayer || GenTicks.TicksGame >= offer.ExpiryTick) continue;

                NationalData targetData = offer.TargetFaction.GetNationalData();
                if (targetData?.CurrentPolicy == null) { RejectTradeOffer(offer); continue; }

                if (GetDiplomaticState(offer.ProposingFaction, offer.TargetFaction) == DiplomaticState.AtWar) { RejectTradeOffer(offer); continue; }

                float benefit = offer.ResourcesOffered.Sum(o => o.Value * TradeOffer.GetResourceMarketValue(o.Key)) - offer.ResourcesRequested.Sum(r => r.Value * TradeOffer.GetResourceMarketValue(r.Key));

                if (benefit > 0 && Rand.Value < 0.7f * targetData.CurrentPolicy.tradeInterestFactor) AcceptTradeOffer(offer);
                else RejectTradeOffer(offer);
            }
        }
    }
} 