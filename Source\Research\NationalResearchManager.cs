using Verse;
using System.Collections.Generic;
using System.Linq;
using AIGen.NationalSystemMod.Economy;
using AIGen.NationalSystemMod.Nation;
using AIGen.NationalSystemMod.Defs;
using AIGen.NationalSystemMod.Common;
using AIGen.NationalSystemMod.Utils;
using RimWorld;

namespace AIGen.NationalSystemMod.Research
{
    public class NationalResearchManager : IExposable
    {
        private Faction _faction; // Reference to the owning faction.

        private NationalResearchDef _currentResearch;
        private float _currentResearchProgress; // Progress towards current research.
        private List<NationalResearchDef> _completedResearch;

        public NationalResearchManager()
        {
            _completedResearch = new List<NationalResearchDef>();
        }

        public NationalResearchManager(Faction faction) : this() 
        {
            _faction = faction;
        }

        public NationalResearchDef CurrentResearch => _currentResearch;
        public float CurrentResearchProgress => _currentResearchProgress;
        public float CurrentResearchCost => _currentResearch?.researchCosts.Sum(c => c.value * TradeOffer.GetResourceMarketValue(c.key)) ?? 0f;
        public List<NationalResearchDef> CompletedResearch => _completedResearch;

        public bool IsResearchCompleted(NationalResearchDef def)
        {
            return _completedResearch.Contains(def);
        }

        public bool CanStartResearch(NationalResearchDef def)
        {
            if (_currentResearch == def) return false;
            if (IsResearchCompleted(def)) return false;
            if (def.preresearched != null)
            {
                foreach (NationalResearchDef prereq in def.preresearched)
                {
                    if (!IsResearchCompleted(prereq)) return false;
                }
            }
            return true;
        }

        public void StartResearch(NationalResearchDef def)
        {
            if (!CanStartResearch(def))
            {
                Log.Warning($"[NationalSystemMod] {def.label} cannot be started by {_faction.Name}.");
                return;
            }
            _currentResearch = def;
            _currentResearchProgress = 0f;
            Log.Message($"[NationalSystemMod] {_faction.Name} started researching {def.label}.");
            if (_faction.IsPlayer) Messages.Message($"Your nation has begun researching: {def.label}", MessageTypeDefOf.NeutralEvent);
        }

        public void ResearchTick()
        {
            if (_currentResearch == null) return;

            NationalData nationalData = _faction.GetNationalData();
            if (nationalData == null) return;

            float researchRate = (nationalData.NationalWealth / 1000f) * 0.5f; 
            researchRate += nationalData.Resources.GetResourceAmount(NationalResourceType.Components) * 0.1f;
            researchRate += nationalData.Resources.GetResourceAmount(NationalResourceType.AdvancedComponents) * 0.5f;

            if (nationalData.CurrentPolicy != null)
            {
                researchRate *= nationalData.CurrentPolicy.researchSpeedModifier;
            }

            float progressPerTick = researchRate / GenDate.TicksPerDay; 

            bool canAfford = true;
            foreach (var costEntry in _currentResearch.researchCosts)
            {
                float costThisTick = costEntry.value / GenDate.TicksPerDay;
                if (!nationalData.Resources.TryConsumeResource(costEntry.key, costThisTick))
                {
                    canAfford = false;
                    break;
                }
            }

            if (canAfford)
            {
                _currentResearchProgress += progressPerTick;
            }
            else
            {
                _currentResearchProgress += progressPerTick * 0.1f; 
                if (_faction.IsPlayer && GenTicks.TicksGame % (GenDate.TicksPerDay * 5) == 0)
                {
                    Messages.Message($"National research is slowing due to lack of resources for {_currentResearch.label}.", MessageTypeDefOf.NegativeEvent);
                }
            }

            float totalCostEquivalent = _currentResearch.researchCosts.Sum(c => c.value * TradeOffer.GetResourceMarketValue(c.key));

            if (_currentResearchProgress >= totalCostEquivalent)
            {
                CompleteResearch(_currentResearch);
            }
        }

        private void CompleteResearch(NationalResearchDef def)
        {
            _completedResearch.Add(def);
            Log.Message($"[NationalSystemMod] {_faction.Name} completed researching {def.label}.");
            if (_faction.IsPlayer) Messages.Message($"Your nation completed researching: {def.label}!", MessageTypeDefOf.PositiveEvent);

            NationalData nationalData = _faction.GetNationalData();
            if (nationalData != null)
            {
                nationalData.MilitaryPower += def.militaryPowerBonus;
                if (def.unlockedPolicy != null)
                {
                    Log.Message($"[NationalSystemMod] {def.label} unlocked new policy: {def.unlockedPolicy.label}");
                }
                if (def.customEffectTags != null)
                {
                    foreach (string tag in def.customEffectTags)
                    {
                        Log.Message($"[NationalSystemMod] Custom effect from {def.label}: {tag}");
                    }
                }
            }
            
            _currentResearch = null;
            _currentResearchProgress = 0f;
        }

        public void ExposeData()
        {
            Scribe_References.Look(ref _faction, "faction");
            Scribe_Defs.Look(ref _currentResearch, "currentResearch");
            Scribe_Values.Look(ref _currentResearchProgress, "currentResearchProgress", 0f);
            Scribe_Collections.Look(ref _completedResearch, "completedResearch", LookMode.Def);
            if (Scribe.mode == LoadSaveMode.PostLoadInit && _completedResearch == null)
            {
                _completedResearch = new List<NationalResearchDef>();
            }
        }
    }
} 