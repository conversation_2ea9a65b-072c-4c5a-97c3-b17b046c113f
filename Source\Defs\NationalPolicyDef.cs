using Verse;
using System.Collections.Generic;
using AIGen.NationalSystemMod.Economy;
using AIGen.NationalSystemMod.StrategicAI; // For AIStrategicGoalType
using AIGen.NationalSystemMod.Research;

namespace AIGen.NationalSystemMod.Defs // New namespace for Defs.
{
    // This Def represents a National Policy or Ideology that a faction can have.
    public class NationalPolicyDef : Def
    {
        public new string description;

        // Modifiers for AI behavior and game mechanics.
        public float influenceSpreadModifier = 1.0f; // Multiplier for influence gained from tiles.
        public float militaryAggressionFactor = 1.0f; // Multiplier for AI's willingness to declare war/attack.
        public float diplomaticAggressionFactor = 1.0f; // Multiplier for how often AI demands things.
        public float tradeInterestFactor = 1.0f; // Multiplier for AI's willingness to engage in trade.
        public float researchSpeedModifier = 1.0f; // Multiplier for research progress.

        // AI weights for strategic goals
        public float aiEconomicWeight = 1.0f;
        public float aiMilitaryWeight = 1.0f;
        public float aiExpansionWeight = 1.0f;
        public float aiTechWeight = 1.0f;
        public float aiDiplomacyWeight = 1.0f;

        // Policy adoption
        public bool isDefault = false;
        public float adoptionCost = 0f;

        // Resource production modifiers
        public float foodProductionFactor = 1.0f;
        public float silverProductionFactor = 1.0f;
        public float componentProductionFactor = 1.0f;
        public float productionEfficiencyModifier = 1.0f; // Multiplier for overall resource production.

        // Flavor text or description for the policy.
        public string policyDescription;

        // Optionally, define specific incident types that this policy favors/disfavors.
        // public List<IncidentDef> favoredIncidents;
        // public List<IncidentDef> disfavoredIncidents;

        // Custom icon path (optional)
        // public string iconPath;

        // NEW: Ideal conditions/synergies for AI evaluation.
        public List<Research.NationalResearchDef.ResearchCost> idealResourceLevels; // E.g., {Food: 200, Steel: 100} means favorable if these resources are high.
        public float idealMilitaryPowerMin = 0f; // Ideal if military power is above this.
        public AIStrategicGoalType favoredGoalType = AIStrategicGoalType.None; // AI will favor this policy if pursuing this goal.
        public List<NationalResearchDef> favoredResearchCompleted; // Policy is more attractive if these are completed.

        public override IEnumerable<string> ConfigErrors()
        {
            foreach (string error in base.ConfigErrors()) yield return error;
            if (label.NullOrEmpty()) yield return "NationalPolicyDef needs a label.";
            if (policyDescription.NullOrEmpty()) yield return "NationalPolicyDef needs a policyDescription.";
        }

        public override void PostLoad()
        {
            base.PostLoad();
        }
    }
} 