using Verse;
using System.Collections.Generic;

namespace AIGen.NationalSystemMod.Defs // New namespace for Defs.
{
    // This Def represents a National Policy or Ideology that a faction can have.
    public class NationalPolicyDef : Def
    {
        // Modifiers for AI behavior and game mechanics.
        public float influenceSpreadModifier = 1.0f; // Multiplier for influence spread rate.
        public float militaryAggressionFactor = 1.0f; // Multiplier for how aggressive AI is in military expansion.
        public float diplomaticAggressionFactor = 1.0f; // Multiplier for how often AI demands things.
        public float tradeInterestFactor = 1.0f; // Multiplier for AI's interest in trade events.
        public float researchSpeedModifier = 1.0f; // Multiplier for research speed.

        // Flavor text or description for the policy.
        public string policyDescription;

        // Optionally, define specific incident types that this policy favors/disfavors.
        // public List<IncidentDef> favoredIncidents;
        // public List<IncidentDef> disfavoredIncidents;

        // Custom icon path (optional)
        // public string iconPath;

        public override IEnumerable<string> ConfigErrors()
        {
            foreach (string error in base.ConfigErrors()) yield return error;
            if (label.NullOrEmpty()) yield return "NationalPolicyDef needs a label.";
            if (policyDescription.NullOrEmpty()) yield return "NationalPolicyDef needs a policyDescription.";
        }
    }
} 