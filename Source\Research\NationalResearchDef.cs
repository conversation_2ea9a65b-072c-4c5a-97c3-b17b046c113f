using Verse;
using System.Collections.Generic;
using AIGen.NationalSystemMod.Economy;
using AIGen.NationalSystemMod.Defs; // For NationalPolicyDef
using System.Linq;

namespace AIGen.NationalSystemMod.Research // New namespace for Research
{
    public class NationalResearchDef : Def
    {
        public string description;
        public List<NationalResearchDef> preresearched; // Prerequisites for this research.

        // Costs to research.
        public List<ResearchCost> researchCosts;

        // Effects of completing this research.
        public float militaryPowerBonus = 0f;
        public float influenceSpreadBonus = 0f;
        public float resourceProductionBonus = 0f; // Multiplier for all resource production.
        public float militaryUpkeepReduction = 0f; // Percentage reduction.
        public float tradeInterestBonus = 0f; // For AI policy calculations.
        public NationalPolicyDef unlockedPolicy; // Research can unlock new policies.

        // Custom effects (e.g., unlocks a new building, a special ability for armies).
        // This could be a list of generic string "effect tags" or more complex objects.
        public List<string> customEffectTags; 

        public class ResearchCost
        {
            public NationalResourceType key;
            public float value;
        }


        public override void PostLoad()
        {
            base.PostLoad();
        }

        public override IEnumerable<string> ConfigErrors()
        {
            foreach (string error in base.ConfigErrors()) yield return error;
            if (researchCosts == null || !researchCosts.Any())
            {
                yield return "NationalResearchDef requires researchCosts.";
            }
        }
    }
} 