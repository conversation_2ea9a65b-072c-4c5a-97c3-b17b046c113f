using Verse;
using RimWorld;
using AIGen.NationalSystemMod.Nation; // For NationalData
using AIGen.NationalSystemMod.Utils; // For FactionExtensions
using System.Collections.Generic;

namespace AIGen.NationalSystemMod.Economy // New namespace for economy-related classes.
{
    // Represents a persistent trade agreement between two factions.
    public class TradeAgreement : IExposable
    {
        public Faction FactionA;
        public Faction FactionB;
        public int StartTick;
        public int DurationTicks; // How long the agreement lasts (e.g., 60 days).
        public float BaseTradeValue; // Base value exchanged periodically.

        private const int TradePayoutIntervalTicks = GenDate.TicksPerDay * 5; // Trade occurs every 5 days.
        private int lastPayoutTick;

        public bool IsActive => GenTicks.TicksGame < StartTick + DurationTicks;

        public TradeAgreement() { } // Parameterless constructor for Scribe.

        public TradeAgreement(Faction factionA, Faction factionB, int durationDays, float baseTradeValue)
        {
            FactionA = factionA;
            FactionB = factionB;
            StartTick = GenTicks.TicksGame;
            DurationTicks = durationDays * GenDate.TicksPerDay;
            BaseTradeValue = baseTradeValue;
            lastPayoutTick = StartTick;
        }

        public void ExposeData()
        {
            Scribe_References.Look(ref FactionA, "factionA");
            Scribe_References.Look(ref FactionB, "factionB");
            Scribe_Values.Look(ref StartTick, "startTick");
            Scribe_Values.Look(ref DurationTicks, "durationTicks");
            Scribe_Values.Look(ref BaseTradeValue, "baseTradeValue");
            Scribe_Values.Look(ref lastPayoutTick, "lastPayoutTick");
        }

        // Simulates a trade exchange and applies benefits.
        public void DoTradePayout()
        {
            if (!IsActive) return;

            if (GenTicks.TicksGame >= lastPayoutTick + TradePayoutIntervalTicks)
            {
                // Ensure both factions still exist and have NationalData.
                if (FactionA == null || FactionB == null) return;
                NationalData dataA = FactionA.GetNationalData();
                NationalData dataB = FactionB.GetNationalData();
                if (dataA == null || dataB == null) return;

                // Calculate actual trade value based on base value, policies, and wealth.
                float actualTradeValue = BaseTradeValue;
                if(dataA.CurrentPolicy != null) actualTradeValue *= dataA.CurrentPolicy.tradeInterestFactor;
                if(dataB.CurrentPolicy != null) actualTradeValue *= dataB.CurrentPolicy.tradeInterestFactor;
                actualTradeValue *= (dataA.NationalWealth + dataB.NationalWealth) / 2000f; // Scale by combined wealth.

                // Apply wealth gain.
                dataA.NationalWealth += actualTradeValue * 0.5f; // Each gains half.
                dataB.NationalWealth += actualTradeValue * 0.5f;

                // Minor goodwill gain for ongoing trade.
                FactionA.TryAffectGoodwillWith(FactionB, 1); // Small, slow goodwill gain.

                lastPayoutTick = GenTicks.TicksGame;

                // Log if player is involved.
                if (FactionA.IsPlayer || FactionB.IsPlayer)
                {
                    Messages.Message($"Trade agreement between {FactionA.Name} and {FactionB.Name} yielded {actualTradeValue:F0} wealth.", MessageTypeDefOf.PositiveEvent, false);
                }
            }
        }
    }
} 