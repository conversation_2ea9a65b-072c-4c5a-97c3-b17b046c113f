using Verse; // Essential RimWorld base namespace for core functionalities.
using HarmonyLib; // Necessary for patching game methods later on.
using System.Reflection; // Used for reflecting on assemblies, especially for Harmony.
using AIGen.NationalSystemMod.Common; // Add this using directive.
using AIGen.NationalSystemMod.World; // Add this using directive for NationalBorderDrawer.
using AIGen.NationalSystemMod.UI; // NEW: Add this using directive.
using UnityEngine;
using System.Linq; // For Faction selection
using RimWorld; // For Messages, MessageTypeDefOf
using RimWorld.Planet; // For WorldInspectPaneUtility, WorldSelector, Tile
using System.Collections.Generic; // For List

namespace AIGen.NationalSystemMod // IMPORTANT: Replace with your actual package ID from About.xml.
{
    // [StaticConstructorOnStartup] ensures this class's static constructor runs when the game loads.
    [StaticConstructorOnStartup]
    public class NationalSystemMod : Mod
    {
        // A static instance to easily access our mod's main class from anywhere.
        public static NationalSystemMod Instance { get; private set; } 

        // The constructor runs when the mod is loaded by RimWorld.
        public NationalSystemMod(ModContentPack content) : base(content)
        {
            Instance = this; // Set the singleton instance.

            // Initialize Harmony to patch game code. This will be crucial for integrating with RimWorld's systems.
            var harmony = new Harmony("AIGen.NationalSystemMod"); // IMPORTANT: Use your actual package ID here too.
            harmony.PatchAll(Assembly.GetExecutingAssembly()); // Apply all Harmony patches defined in this assembly.

            // A simple log message to confirm the mod has started loading.
            // This is primarily for debugging; avoid excessive logging in production code.
            Log.Message("[NationalSystemMod] initialized successfully."); 
        }

        // Future placeholder: Methods for handling mod settings UI could go here.
        
        public override void DoSettingsWindowContents(Rect inRect)
        {
            // UI code for mod settings.
        }

        public override string SettingsCategory()
        {
            return "National System Mod"; // Name for the mod settings tab.
        }
        

        // --- NEW: Harmony Patch to call CoreLogic.InitializeModSystems after world load ---
        
        [HarmonyPatch(typeof(Game), "FinalizeInit")] // Patch the method that runs after world init
        public static class Game_FinalizeInit_Patch
        {
            [HarmonyPostfix] // Run after the original method
            public static void Postfix()
            {
                Log.Message("[NationalSystemMod.HarmonyPatch] Game.FinalizeInit Postfix called. Initializing CoreLogic systems.");
                CoreLogic.InitializeModSystems(); // Call our core initialization method
            }
        }

        // --- NEW: Harmony Patch to draw borders on the world map ---
        [HarmonyPatch(typeof(RimWorld.Planet.World), "WorldUpdate")] // Patch the World.WorldUpdate method
        public static class World_WorldUpdate_Patch
        {
            [HarmonyPostfix] // Run after the original World.WorldUpdate method
            public static void Postfix()
            {
                NationalBorderDrawer.DrawNationalBorders(); // Call our custom border drawing method
            }
        }

        // --- NEW: Harmony Patch for daily updates ---
        private static int lastDailyTick = -1; // To track when the last daily update happened

        [HarmonyPatch(typeof(TickManager), "DoSingleTick")] // Patch the method that runs every tick
        public static class TickManager_DoSingleTick_Patch
        {
            [HarmonyPostfix] // Run after the original method
            public static void Postfix()
            {
                // Only run CoreLogic.PeriodicUpdate once per game day.
                // 60000 ticks = 1 game day.
                if (GenTicks.TicksGame % GenDate.TicksPerDay == 0 && GenTicks.TicksGame != lastDailyTick)
                {
                    // Ensure it runs only once per day, not multiple times if conditions are met mid-tick.
                    lastDailyTick = GenTicks.TicksGame; 
                    
                    //Log.Message($"[NationalSystemMod.HarmonyPatch] Daily update triggered at tick {GenTicks.TicksGame}.");
                    CoreLogic.PeriodicUpdate();
                }
            }
        }

        // --- NEW: Harmony Patch to add a UI button to the World Map ---
        [HarmonyPatch(typeof(RimWorld.Planet.WorldGlobalControls), "WorldGlobalControlsOnGUI")]
        public static class WorldGlobalControls_OnGUI_Patch
        {
            [HarmonyPostfix] // Run after the original method to add our button.
            public static void Postfix()
            {
                // Only show our UI button when on the world map.
                if (Find.World == null || Find.CurrentMap != null)
                {
                    return;
                }

                // Define button position (adjust as needed).
                float xOffsetFromRight = 150f;
                float yPosition = 4f; 
                float buttonWidth = 140f;
                float buttonHeight = 28f;

                Rect buttonRect = new Rect(Screen.width - xOffsetFromRight, yPosition, buttonWidth, buttonHeight);

                if (Widgets.ButtonText(buttonRect, "National Influence"))
                {
                    // Open our custom InfluenceUI window.
                    Find.WindowStack.Add(new InfluenceUI());
                }
            }
        }

        // --- NEW: Harmony Patch to add a "Request Support" button to World Tile Inspector ---
        [HarmonyPatch(typeof(RimWorld.Planet.WorldInspectPane), "DoPaneContents")]
        public static class WorldInspectPane_DoPaneContents_Patch
        {
            [HarmonyPostfix]
            public static void Postfix(Rect rect)
            {
                var selectedObject = Find.WorldSelector.SingleSelectedObject;
                if (selectedObject == null) return;

                int tileID = selectedObject.Tile;
                InfluenceTileData tileInfluence = CoreLogic.WorldInfluenceMap?.GetInfluenceData(tileID);

                float buttonWidth = 150f;
                float buttonHeight = 30f;
                float margin = 5f;
                
                float currentY = rect.yMax - 120f; // Adjusted starting Y to accommodate more buttons

                if (!CoreLogic.IsModSystemInitialized)
                {
                    GUI.color = Color.gray;
                    Widgets.ButtonText(new Rect(rect.x + (rect.width - buttonWidth) / 2f, currentY, buttonWidth, buttonHeight), "Mod Not Ready");
                    GUI.color = Color.white;
                    return;
                }

                // --- Player Claim Tile Button ---
                Rect claimButtonRect = new Rect(rect.x + (rect.width - buttonWidth) / 2f, currentY, buttonWidth, buttonHeight);
                if (Widgets.ButtonText(claimButtonRect, "Claim Tile (Player)"))
                {
                    InfluenceManager.PlayerClaimTile(tileID, 0.8f);
                }
                currentY -= (buttonHeight + margin);

                // --- Request Support Button ---
                Rect requestButtonRect = new Rect(rect.x + (rect.width - buttonWidth) / 2f, currentY, buttonWidth, buttonHeight);
                if (Widgets.ButtonText(requestButtonRect, "Request Influence Support"))
                {
                    Faction playerFaction = Faction.OfPlayer;
                    List<Faction> potentialAllies = Find.FactionManager.AllFactionsListForReading
                        .Where(f => !f.IsPlayer && !f.Hidden && !f.HostileTo(playerFaction) && f.def.humanlikeFaction)
                        .ToList();
                    
                    if (potentialAllies.Any())
                    {
                        List<FloatMenuOption> options = new List<FloatMenuOption>();
                        foreach (Faction ally in potentialAllies)
                        {
                            options.Add(new FloatMenuOption(
                                $"{ally.Name} (Relation: {playerFaction.GoodwillWith(ally):F0})", 
                                () => InfluenceManager.RequestInfluenceSupport(playerFaction, ally, tileID, 0.5f)
                            ));
                        }
                        Find.WindowStack.Add(new FloatMenu(options));
                    }
                    else
                    {
                        Messages.Message("No suitable factions found to request influence support from.", MessageTypeDefOf.RejectInput, false);
                    }
                }
                currentY -= (buttonHeight + margin);

                // --- NEW: Demand Tile Button ---
                if (tileInfluence != null && tileInfluence.faction != null && !tileInfluence.faction.IsPlayer)
                {
                    Rect demandButtonRect = new Rect(rect.x + (rect.width - buttonWidth) / 2f, currentY, buttonWidth, buttonHeight);
                    if (Widgets.ButtonText(demandButtonRect, $"Demand Tile from {tileInfluence.faction.Name}"))
                    {
                        InfluenceManager.PlayerDemandTile(tileInfluence.faction, tileID);
                    }
                }
            }
        }
        
    }
} 