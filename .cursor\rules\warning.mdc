---
description: 
globs: 
alwaysApply: true
---
1. Phrases and Items to Exclude from Code Generation
Cursor AI should intentionally exclude the following elements from the C# code it generates:

Console Output Related Code:
Avoid using System.Console; or Console.WriteLine(); and similar console output statements. Direct console output within RimWorld can impact game performance. For debugging during mod development, it's standard to use Harmony Debugging or a custom logging system. Unnecessary console output should be excluded.

Direct File System Access (Unless Absolutely Necessary):
Avoid logic that directly reads from or writes to files outside the game using the System.IO namespace, unless there's a specific, justified purpose (e.g., loading configuration files, external data). Mod data should be managed through RimWorld's standard methods (Defs, SaveData).

External Network Communication Code:
RimWorld mods generally operate in an offline environment. Code that attempts to communicate with external servers or requires internet connectivity should be excluded. This can lead to security and performance issues.

Unnecessary UI/Graphics Library Imports:
RimWorld has its own UI system and graphics rendering pipeline. Exclude imports for UI/graphics-related libraries that are incompatible with the RimWorld environment, such as System.Drawing, Windows.Forms, or WPF.

Excessive General-Purpose Utility Classes:
Prioritize utility functions or classes specifically tailored to the RimWorld modding environment. General C# utilities (e.g., complex math libraries, excessive use of standard libraries for serialization/deserialization) should be used minimally and only when truly necessary.

Indiscriminate Use of Global/Static Variables:
These can cause data consistency issues and lead to tick generation. Instruct Cursor AI to use them cautiously and only when essential.


2. Key Cautions During Coding (Best Practices)
Emphasize to Cursor AI that it must pay special attention to the following points when generating code:

Prioritize RimWorld API and Harmony Library Usage:
RimWorld mods extend and patch existing game code through the game's internal API and the Harmony library. Instruct Cursor AI to correctly understand and leverage these RimWorld-specific frameworks. It should not rely solely on general C# coding practices.

Constant Vigilance for Tick Optimization:
This is paramount. Encourage Cursor AI to continually ask, "Does this code minimize its impact on game ticks?" for every piece of code it writes.
Minimize Tick-Based Loops: Especially for loops that iterate over the world map or many objects, instruct it to use for loops instead of foreach where beneficial, and apply conditions to skip unnecessary iterations.

Utilize Asynchronous/Lazy Loading: Complex calculations or data loading should be separated from game ticks by handling them asynchronously or deferring loading until absolutely necessary.

Minimize Object Allocation:
Frequent creation and destruction of new objects every tick can trigger Garbage Collection (GC), a major source of game lag. Instruct Cursor AI to consider object reuse and pooling patterns.

Accurate Integration with XML (Defs) Definitions:
All data defined by the mod (buildings, factions, events, etc.) must be defined in XML files (Defs/). Ensure Cursor AI correctly references and uses these XML definitions in the C# code. Encourage the use of XML definitions over hardcoded values.

Prevent NullReferenceExceptions:
This is one of the most common errors in RimWorld modding. Instruct Cursor AI to rigorously check for null before referencing any object. Special care should be taken with references to objects that can be dynamically created or destroyed within the game (e.g., pawns, buildings, world objects).

Consider Serialization and Saving System:
Any new data introduced by the mod (e.g., national influence, diplomatic relations) must be correctly serialized and restored when the game is saved and loaded. Cursor AI should design the code to use the IExposable interface or Scribe_ classes to ensure data is safely written to save files.


Code Readability and Maintainability: Even though AI generates the code, human readability is crucial for future debugging or feature additions. Request appropriate comments, clear variable and function names, and a modular structure.